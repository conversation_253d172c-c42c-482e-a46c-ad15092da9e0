<Entitys>
    <Entity>
        <EntityId>entity_1</EntityId>
        <EntityName>IEXPL0RE RAT</EntityName>
        <EntityVariantNames>
            <EntityVariantName>Sharky RAT</EntityVariantName>
            <EntityVariantName>c0d0so0</EntityVariantName>
            <EntityVariantName>Backdoor.Briba</EntityVariantName>
        </EntityVariantNames>
        <EntityType>tool</EntityType>
        <EntitySubType>malware</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
            <Label>TA0002</Label>
            <Label>TA0003</Label>
            <Label>TA0009</Label>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>1</Time>
        </Times>
        <Properties>
            <Property name="type">Remote Access Trojan</Property>
            <Property name="capabilities">Keylogging, file manipulation, remote control</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_2</EntityId>
        <EntityName>APT-29</EntityName>
        <EntityVariantNames>
            <EntityVariantName>Cozy Bear</EntityVariantName>
        </EntityVariantNames>
        <EntityType>attcker</EntityType>
        <EntitySubType>org</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
            <Label>TA0002</Label>
            <Label>TA0003</Label>
            <Label>TA0009</Label>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>1</Time>
        </Times>
        <Properties>
            <Property name="country">Russia</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_3</EntityId>
        <EntityName>Human Rights NGO</EntityName>
        <EntityType>vctim</EntityType>
        <EntitySubType>org</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
        </Labels>
        <Times>
            <Time>2</Time>
        </Times>
    </Entity>
    <Entity>
        <EntityId>entity_4</EntityId>
        <EntityName>News Organization</EntityName>
        <EntityType>vctim</EntityType>
        <EntitySubType>org</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
        </Labels>
        <Times>
            <Time>2</Time>
        </Times>
        <Properties>
            <Property name="focus">China developments</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_5</EntityId>
        <EntityName>Tibet-related Organization</EntityName>
        <EntityType>vctim</EntityType>
        <EntitySubType>org</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
        </Labels>
        <Times>
            <Time>2</Time>
        </Times>
    </Entity>
    <Entity>
        <EntityId>entity_6</EntityId>
        <EntityName>Social Engineering</EntityName>
        <EntityType>tool</EntityType>
        <EntitySubType>method</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
        </Labels>
        <Times>
            <Time>1</Time>
        </Times>
    </Entity>
    <Entity>
        <EntityId>entity_7</EntityId>
        <EntityName>Phishing Email</EntityName>
        <EntityType>tool</EntityType>
        <EntitySubType>method</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
        </Labels>
        <Times>
            <Time>1</Time>
        </Times>
    </Entity>
    <Entity>
        <EntityId>entity_8</EntityId>
        <EntityName>d7c826ac94522416a0aecf5b7a5d2afe</EntityName>
        <EntityType>ioc</EntityType>
        <EntitySubType>hash</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
        </Labels>
        <Times>
            <Time>2</Time>
        </Times>
        <Properties>
            <Property name="type">MD5 hash</Property>
            <Property name="target">Organization 1</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_9</EntityId>
        <EntityName>66e1aff355c29c6f39b21aedbbed2d5c</EntityName>
        <EntityType>ioc</EntityType>
        <EntitySubType>hash</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
        </Labels>
        <Times>
            <Time>2</Time>
        </Times>
        <Properties>
            <Property name="type">MD5 hash</Property>
            <Property name="target">Organization 2</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_10</EntityId>
        <EntityName>Cobalt Strike</EntityName>
        <EntityType>tool</EntityType>
        <EntitySubType>tool</EntitySubType>
        <Labels>
            <Label>TA0002</Label>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>3</Time>
        </Times>
    </Entity>
    <Entity>
        <EntityId>entity_11</EntityId>
        <EntityName>Windows System</EntityName>
        <EntityType>env</EntityType>
        <EntitySubType>os</EntitySubType>
        <Labels>
            <Label>TA0002</Label>
        </Labels>
        <Times>
            <Time>2</Time>
        </Times>
    </Entity>
    <Entity>
        <EntityId>entity_12</EntityId>
        <EntityName>update.microsoft.com</EntityName>
        <EntityType>ioc</EntityType>
        <EntitySubType>domain</EntitySubType>
        <Labels>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>3</Time>
        </Times>
        <Properties>
            <Property name="purpose">C2 communication</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_13</EntityId>
        <EntityName>svchost.exe</EntityName>
        <EntityType>file</EntityType>
        <EntitySubType>file</EntitySubType>
        <Labels>
            <Label>TA0002</Label>
            <Label>TA0005</Label>
        </Labels>
        <Times>
            <Time>3</Time>
        </Times>
        <Properties>
            <Property name="abuse">Process injection</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_14</EntityId>
        <EntityName>perf*.dat</EntityName>
        <EntityType>file</EntityType>
        <EntitySubType>file</EntitySubType>
        <Labels>
            <Label>TA0002</Label>
        </Labels>
        <Times>
            <Time>3</Time>
        </Times>
        <Properties>
            <Property name="location">%temp% folder</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_15</EntityId>
        <EntityName>ContainerV2</EntityName>
        <EntityType>file</EntityType>
        <EntitySubType>file</EntitySubType>
        <Labels>
            <Label>TA0002</Label>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>3</Time>
        </Times>
    </Entity>
    <Entity>
        <EntityId>entity_16</EntityId>
        <EntityName>client.dll</EntityName>
        <EntityType>file</EntityType>
        <EntitySubType>file</EntitySubType>
        <Labels>
            <Label>TA0002</Label>
            <Label>TA0009</Label>
        </Labels>
        <Times>
            <Time>4</Time>
        </Times>
    </Entity>
    <Entity>
        <EntityId>entity_17</EntityId>
        <EntityName>IEXPL0RE.EXE</EntityName>
        <EntityType>file</EntityType>
        <EntitySubType>file</EntitySubType>
        <Labels>
            <Label>TA0002</Label>
            <Label>TA0003</Label>
        </Labels>
        <Times>
            <Time>2</Time>
        </Times>
        <Properties>
            <Property name="location">Application Data\Microsoft\Internet Explorer</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_18</EntityId>
        <EntityName>IEXPL0RE.LNK</EntityName>
        <EntityType>file</EntityType>
        <EntitySubType>file</EntitySubType>
        <Labels>
            <Label>TA0003</Label>
        </Labels>
        <Times>
            <Time>2</Time>
        </Times>
        <Properties>
            <Property name="location">Start Menu\Programs\Startup</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_19</EntityId>
        <EntityName>lock.dat</EntityName>
        <EntityType>file</EntityType>
        <EntitySubType>file</EntitySubType>
        <Labels>
            <Label>TA0009</Label>
        </Labels>
        <Times>
            <Time>3</Time>
        </Times>
        <Properties>
            <Property name="location">C:\WINDOWS\system</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_20</EntityId>
        <EntityName>MSMAPI32.SRG</EntityName>
        <EntityType>file</EntityType>
        <EntitySubType>file</EntitySubType>
        <Labels>
            <Label>TA0009</Label>
        </Labels>
        <Times>
            <Time>3</Time>
        </Times>
        <Properties>
            <Property name="location">C:\WINDOWS\system</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_21</EntityId>
        <EntityName>STREAM.SYS</EntityName>
        <EntityType>file</EntityType>
        <EntitySubType>file</EntitySubType>
        <Labels>
            <Label>TA0009</Label>
        </Labels>
        <Times>
            <Time>3</Time>
        </Times>
        <Properties>
            <Property name="location">C:\WINDOWS\system32</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_22</EntityId>
        <EntityName>Command and Control Server</EntityName>
        <EntityType>asset</EntityType>
        <EntitySubType>ip</EntitySubType>
        <Labels>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>3</Time>
        </Times>
        <Properties>
            <Property name="location">China</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_23</EntityId>
        <EntityName>XOR 0xCD</EntityName>
        <EntityType>tool</EntityType>
        <EntitySubType>method</EntitySubType>
        <Labels>
            <Label>TA0005</Label>
        </Labels>
        <Times>
            <Time>3</Time>
        </Times>
        <Properties>
            <Property name="purpose">Encryption</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_24</EntityId>
        <EntityName>StrokeIt</EntityName>
        <EntityType>env</EntityType>
        <EntitySubType>software</EntitySubType>
        <Labels>
            <Label>TA0002</Label>
        </Labels>
        <Times>
            <Time>2</Time>
        </Times>
        <Properties>
            <Property name="vulnerability">DLL injection</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_25</EntityId>
        <EntityName>config.dll</EntityName>
        <EntityType>file</EntityType>
        <EntitySubType>file</EntitySubType>
        <Labels>
            <Label>TA0002</Label>
        </Labels>
        <Times>
            <Time>2</Time>
        </Times>
    </Entity>
</Entitys>
