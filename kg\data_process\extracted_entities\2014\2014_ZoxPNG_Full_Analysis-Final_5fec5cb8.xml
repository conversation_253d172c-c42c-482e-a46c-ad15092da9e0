<Entitys>
    <Entity>
        <EntityId>entity_1</EntityId>
        <EntityName>ZoxPNG</EntityName>
        <EntityType>tool</EntityType>
        <EntitySubType>malware</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
            <Label>TA0002</Label>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>1</Time>
        </Times>
        <Properties>
            <Property name="type">RAT</Property>
            <Property name="carrier">PNG image file format</Property>
            <Property name="commands">13 native commands</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_2</EntityId>
        <EntityName>C2 Server</EntityName>
        <EntityType>asset</EntityType>
        <EntitySubType>domain</EntitySubType>
        <Labels>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>2</Time>
        </Times>
    </Entity>
    <Entity>
        <EntityId>entity_3</EntityId>
        <EntityName>WinInet API</EntityName>
        <EntityType>env</EntityType>
        <EntitySubType>software</EntitySubType>
        <Labels>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>3</Time>
        </Times>
    </Entity>
    <Entity>
        <EntityId>entity_4</EntityId>
        <EntityName>VictimSystemData</EntityName>
        <EntityType>ioc</EntityType>
        <EntitySubType>payload</EntitySubType>
        <Labels>
            <Label>TA0007</Label>
        </Labels>
        <Times>
            <Time>4</Time>
        </Times>
        <Properties>
            <Property name="size">52 bytes</Property>
            <Property name="transmission">Base64 encoded in Cookie header</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_5</EntityId>
        <EntityName>HTTP GET/POST requests</EntityName>
        <EntityType>ioc</EntityType>
        <EntitySubType>payload</EntitySubType>
        <Labels>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>5</Time>
        </Times>
        <Properties>
            <Property name="pattern">Specific URL structure with PNG parameters</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_6</EntityId>
        <EntityName>PNG file format</EntityName>
        <EntityType>ioc</EntityType>
        <EntitySubType>payload</EntitySubType>
        <Labels>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>6</Time>
        </Times>
        <Properties>
            <Property name="data_offset">0x21 for size, 0x29 for data</Property>
            <Property name="compression">zlib deflate</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_7</EntityId>
        <EntityName>CommandHeader</EntityName>
        <EntityType>ioc</EntityType>
        <EntitySubType>payload</EntitySubType>
        <Labels>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>7</Time>
        </Times>
        <Properties>
            <Property name="structure">Contains CommandData and payload size</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_8</EntityId>
        <EntityName>CommandData</EntityName>
        <EntityType>ioc</EntityType>
        <EntitySubType>payload</EntitySubType>
        <Labels>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>8</Time>
        </Times>
        <Properties>
            <Property name="fields">dwCommandID, dwCommandSequenceID, dwLastError, dwPayloadSize</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_9</EntityId>
        <EntityName>4NB Corp</EntityName>
        <EntityType>attcker</EntityType>
        <EntitySubType>org</EntitySubType>
        <Labels>
            <Label>TA0042</Label>
        </Labels>
        <Times>
            <Time>9</Time>
        </Times>
        <Properties>
            <Property name="country">South Korea</Property>
            <Property name="role">Certificate signer</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_10</EntityId>
        <EntityName>ZoxRPC</EntityName>
        <EntityType>tool</EntityType>
        <EntitySubType>malware</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
            <Label>TA0002</Label>
        </Labels>
        <Times>
            <Time>10</Time>
        </Times>
        <Properties>
            <Property name="relation">Predecessor to ZoxPNG</Property>
            <Property name="exploit">MS08-067 vulnerability</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_11</EntityId>
        <EntityName>MS08-067</EntityName>
        <EntityType>vul</EntityType>
        <EntitySubType>cve</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
        </Labels>
        <Times>
            <Time>11</Time>
        </Times>
    </Entity>
</Entitys>
<Relationships>
    <Relationship>
        <RelationshipId>relationship_1</RelationshipId>
        <RelationshipType>related_to</RelationshipType>
        <Source>ZoxPNG</Source>
        <Target>C2 Server</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_2</RelationshipId>
        <RelationshipType>use</RelationshipType>
        <Source>ZoxPNG</Source>
        <Target>WinInet API</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_3</RelationshipId>
        <RelationshipType>related_to</RelationshipType>
        <Source>ZoxPNG</Source>
        <Target>VictimSystemData</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_4</RelationshipId>
        <RelationshipType>related_to</RelationshipType>
        <Source>ZoxPNG</Source>
        <Target>HTTP GET/POST requests</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_5</RelationshipId>
        <RelationshipType>related_to</RelationshipType>
        <Source>ZoxPNG</Source>
        <Target>PNG file format</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_6</RelationshipId>
        <RelationshipType>related_to</RelationshipType>
        <Source>ZoxPNG</Source>
        <Target>CommandHeader</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_7</RelationshipId>
        <RelationshipType>related_to</RelationshipType>
        <Source>ZoxPNG</Source>
        <Target>CommandData</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_8</RelationshipId>
        <RelationshipType>use</RelationshipType>
        <Source>4NB Corp</Source>
        <Target>ZoxPNG</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_9</RelationshipId>
        <RelationshipType>related_to</RelationshipType>
        <Source>ZoxPNG</Source>
        <Target>ZoxRPC</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_10</RelationshipId>
        <RelationshipType>use</RelationshipType>
        <Source>ZoxRPC</Source>
        <Target>MS08-067</Target>
    </Relationship>
</Relationships>