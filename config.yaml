
fastapi_server:
  host: localhost
  port: 8000

vector_database:
  path: /rag/data/vector_db

file_upload:
  path: /rag/data/file_uploads

model:
  embedding_model:
    path: /rag/data/embedding_model

milvus:
  auto_start: true
  data_dir: ./milvus_lite
  host: 127.0.0.1
  port: 19530

neo4j:
  auto_start: false
  data_dir: ./neo4j_data
  host: 127.0.0.1
  port: 7687
  http_port: 7474
  # 添加索引器配置
  index_interval: 3600  # 索引间隔，单位为秒，默认1小时
  index_batch_size: 100  # 每次处理的实体数量
  auto_index: true  # 是否自动启动索引器
