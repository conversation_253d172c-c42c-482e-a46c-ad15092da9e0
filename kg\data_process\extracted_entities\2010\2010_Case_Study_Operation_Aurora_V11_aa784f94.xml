<Entitys>
    <Entity>
        <EntityId>entity_1</EntityId>
        <EntityName>Operation Aurora</EntityName>
        <EntityType>event</EntityType>
        <EntitySubType>event</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
            <Label>TA0002</Label>
            <Label>TA0003</Label>
            <Label>TA0005</Label>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>1</Time>
        </Times>
        <Properties>
            <Property name="time">2010</Property>
            <Property name="target">Google</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_2</EntityId>
        <EntityName>Triumfant</EntityName>
        <EntityType>vctim</EntityType>
        <EntitySubType>org</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
        </Labels>
        <Times>
            <Time>1</Time>
        </Times>
        <Properties>
            <Property name="role">Security Research</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_3</EntityId>
        <EntityName>Google</EntityName>
        <EntityType>vctim</EntityType>
        <EntitySubType>org</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
        </Labels>
        <Times>
            <Time>1</Time>
        </Times>
    </Entity>
    <Entity>
        <EntityId>entity_4</EntityId>
        <EntityName>Malicious Dropper</EntityName>
        <EntityType>tool</EntityType>
        <EntitySubType>malware</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
            <Label>TA0002</Label>
        </Labels>
        <Times>
            <Time>2</Time>
        </Times>
        <Properties>
            <Property name="behavior">Creates service keys</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_5</EntityId>
        <EntityName>Anomalous Service</EntityName>
        <EntityType>ioc</EntityType>
        <EntitySubType>payload</EntitySubType>
        <Labels>
            <Label>TA0003</Label>
            <Label>TA0005</Label>
        </Labels>
        <Times>
            <Time>3</Time>
        </Times>
    </Entity>
    <Entity>
        <EntityId>entity_6</EntityId>
        <EntityName>System32 Files</EntityName>
        <EntityType>file</EntityType>
        <EntitySubType>file</EntitySubType>
        <Labels>
            <Label>TA0002</Label>
            <Label>TA0005</Label>
        </Labels>
        <Times>
            <Time>3</Time>
        </Times>
        <Properties>
            <Property name="location">Windows system directory</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_7</EntityId>
        <EntityName>Windows Services</EntityName>
        <EntityType>env</EntityType>
        <EntitySubType>os</EntitySubType>
        <Labels>
            <Label>TA0002</Label>
        </Labels>
        <Times>
            <Time>2</Time>
        </Times>
    </Entity>
    <Entity>
        <EntityId>entity_8</EntityId>
        <EntityName>Triumfant Resolution Manager</EntityName>
        <EntityType>tool</EntityType>
        <EntitySubType>tool</EntitySubType>
        <Labels>
            <Label>TA0005</Label>
        </Labels>
        <Times>
            <Time>4</Time>
        </Times>
        <Properties>
            <Property name="function">Detection and remediation</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_9</EntityId>
        <EntityName>Adaptive Reference Model</EntityName>
        <EntityType>tool</EntityType>
        <EntitySubType>tool</EntitySubType>
        <Labels>
            <Label>TA0005</Label>
        </Labels>
        <Times>
            <Time>4</Time>
        </Times>
        <Properties>
            <Property name="function">Behavior analysis</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_10</EntityId>
        <EntityName>Recognition Filter</EntityName>
        <EntityType>tool</EntityType>
        <EntitySubType>tool</EntitySubType>
        <Labels>
            <Label>TA0005</Label>
        </Labels>
        <Times>
            <Time>5</Time>
        </Times>
        <Properties>
            <Property name="function">Attack pattern detection</Property>
        </Properties>
    </Entity>
</Entitys>
<Relationships>
    <Relationship>
        <RelationshipId>relationship_1</RelationshipId>
        <RelationshipType>involve</RelationshipType>
        <Source>Operation Aurora</Source>
        <Target>Google</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_2</RelationshipId>
        <RelationshipType>involve</RelationshipType>
        <Source>Operation Aurora</Source>
        <Target>Triumfant</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_3</RelationshipId>
        <RelationshipType>use</RelationshipType>
        <Source>Operation Aurora</Source>
        <Target>Malicious Dropper</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_4</RelationshipType>
        <RelationshipType>affect</RelationshipType>
        <Source>Malicious Dropper</Source>
        <Target>Windows Services</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_5</RelationshipId>
        <RelationshipType>related_to</RelationshipType>
        <Source>Malicious Dropper</Source>
        <Target>Anomalous Service</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_6</RelationshipId>
        <RelationshipType>related_to</RelationshipType>
        <Source>Malicious Dropper</Source>
        <Target>System32 Files</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_7</RelationshipId>
        <RelationshipType>belong_to</RelationshipType>
        <Source>System32 Files</Source>
        <Target>Windows Services</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_8</RelationshipId>
        <RelationshipType>trigger</RelationshipType>
        <Source>Google</Source>
        <Target>Malicious Dropper</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_9</RelationshipId>
        <RelationshipType>has</RelationshipType>
        <Source>Google</Source>
        <Target>Windows Services</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_10</RelationshipId>
        <RelationshipType>use</RelationshipType>
        <Source>Triumfant</Source>
        <Target>Triumfant Resolution Manager</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_11</RelationshipId>
        <RelationshipType>use</RelationshipType>
        <Source>Triumfant</Source>
        <Target>Adaptive Reference Model</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_12</RelationshipId>
        <RelationshipType>use</RelationshipType>
        <Source>Triumfant</Source>
        <Target>Recognition Filter</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_13</RelationshipId>
        <RelationshipType>related_to</RelationshipType>
        <Source>Triumfant Resolution Manager</Source>
        <Target>Adaptive Reference Model</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_14</RelationshipId>
        <RelationshipType>related_to</RelationshipType>
        <Source>Adaptive Reference Model</Source>
        <Target>Recognition Filter</Target>
    </Relationship>
</Relationships>