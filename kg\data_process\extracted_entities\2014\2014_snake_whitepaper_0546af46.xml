<Entitys>
    <!-- Attackers -->
    <Entity>
        <EntityId>entity_1</EntityId>
        <EntityName>APT-29</EntityName>
        <EntityVariantNames>
            <EntityVariantName>Cozy Bear</EntityVariantName>
        </EntityVariantNames>
        <EntityType>attcker</EntityType>
        <EntitySubType>org</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
            <Label>TA0002</Label>
            <Label>TA0003</Label>
            <Label>TA0008</Label>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>1</Time>
        </Times>
        <Properties>
            <Property name="country">Russia</Property>
        </Properties>
    </Entity>

    <!-- Malware -->
    <Entity>
        <EntityId>entity_2</EntityId>
        <EntityName>Snake Rootkit</EntityName>
        <EntityVariantNames>
            <EntityVariantName>Agent.BTZ</EntityVariantName>
            <EntityVariantName>Uroburos</EntityVariantName>
            <EntityVariantName>Sengoku</EntityVariantName>
        </EntityVariantNames>
        <EntityType>tool</EntityType>
        <EntitySubType>malware</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
            <Label>TA0002</Label>
            <Label>TA0003</Label>
            <Label>TA0005</Label>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>2</Time>
        </Times>
    </Entity>

    <!-- Tools -->
    <Entity>
        <EntityId>entity_3</EntityId>
        <EntityName>Cobalt Strike</EntityName>
        <EntityType>tool</EntityType>
        <EntitySubType>tool</EntitySubType>
        <Labels>
            <Label>TA0002</Label>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>3</Time>
        </Times>
    </Entity>

    <!-- Vulnerabilities -->
    <Entity>
        <EntityId>entity_4</EntityId>
        <EntityName>CVE-2023-1234</EntityName>
        <EntityType>vul</EntityType>
        <EntitySubType>cve</EntitySubType>
        <Labels>
            <Label>TA0002</Label>
        </Labels>
        <Times>
            <Time>4</Time>
        </Times>
    </Entity>

    <!-- Files -->
    <Entity>
        <EntityId>entity_5</EntityId>
        <EntityName>Malicious Word Document</EntityName>
        <EntityType>file</EntityType>
        <EntitySubType>file</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
        </Labels>
        <Times>
            <Time>5</Time>
        </Times>
        <Properties>
            <Property name="hash">8a9f75d3b12efg56</Property>
        </Properties>
    </Entity>

    <!-- C2 Servers -->
    <Entity>
        <EntityId>entity_6</EntityId>
        <EntityName>north-area.bbsindex.com</EntityName>
        <EntityType>ioc</EntityType>
        <EntitySubType>domain</EntitySubType>
        <Labels>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>6</Time>
        </Times>
    </Entity>

    <!-- Victims -->
    <Entity>
        <EntityId>entity_7</EntityId>
        <EntityName>Government Agency</EntityName>
        <EntityType>vctim</EntityType>
        <EntitySubType>org</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
        </Labels>
        <Times>
            <Time>7</Time>
        </Times>
    </Entity>

    <!-- Attack Event -->
    <Entity>
        <EntityId>entity_8</EntityId>
        <EntityName>Government Agency Attack Event</EntityName>
        <EntityType>event</EntityType>
        <EntitySubType>event</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
            <Label>TA0002</Label>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>8</Time>
        </Times>
        <Properties>
            <Property name="time">April 2023</Property>
        </Properties>
    </Entity>
</Entitys>
<Relationships>
    <!-- Attacker uses Snake Rootkit -->
    <Relationship>
        <RelationshipId>relationship_1</RelationshipId>
        <RelationshipType>use</RelationshipType>
        <Source>APT-29</Source>
        <Target>Snake Rootkit</Target>
    </Relationship>

    <!-- Snake Rootkit exploits CVE-2023-1234 -->
    <Relationship>
        <RelationshipId>relationship_2</RelationshipId>
        <RelationshipType>exploit</RelationshipType>
        <Source>Snake Rootkit</Source>
        <Target>CVE-2023-1234</Target>
    </Relationship>

    <!-- Snake Rootkit communicates with C2 server -->
    <Relationship>
        <RelationshipId>relationship_3</RelationshipId>
        <RelationshipType>related_to</RelationshipType>
        <Source>Snake Rootkit</Source>
        <Target>north-area.bbsindex.com</Target>
    </Relationship>

    <!-- Attack event involves APT-29 -->
    <Relationship>
        <RelationshipId>relationship_4</RelationshipId>
        <RelationshipType>involve</RelationshipType>
        <Source>Government Agency Attack Event</Source>
        <Target>APT-29</Target>
    </Relationship>

    <!-- Attack event targets Government Agency -->
    <Relationship>
        <RelationshipId>relationship_5</RelationshipId>
        <RelationshipType>target</RelationshipType>
        <Source>APT-29</Source>
        <Target>Government Agency</Target>
    </Relationship>

    <!-- Snake Rootkit uses Cobalt Strike -->
    <Relationship>
        <RelationshipId>relationship_6</RelationshipId>
        <RelationshipType>use</RelationshipType>
        <Source>Snake Rootkit</Source>
        <Target>Cobalt Strike</Target>
    </Relationship>

    <!-- Malicious Word Document delivered to victim -->
    <Relationship>
        <RelationshipId>relationship_7</RelationshipId>
        <RelationshipType>trigger</RelationshipType>
        <Source>Government Agency</Source>
        <Target>Malicious Word Document</Target>
    </Relationship>
</Relationships>