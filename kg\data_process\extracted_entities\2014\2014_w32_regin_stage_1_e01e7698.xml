<Entitys>
    <Entity>
        <EntityId>entity_1</EntityId>
        <EntityName>Regin</EntityName>
        <EntityType>attcker</EntityType>
        <EntitySubType>org</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
            <Label>TA0002</Label>
            <Label>TA0003</Label>
            <Label>TA0005</Label>
        </Labels>
        <Times>
            <Time>1</Time>
        </Times>
        <Properties>
            <Property name="stage">Stage #1</Property>
            <Property name="sophistication">High</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_2</EntityId>
        <EntityName>W32/Regin Stage #1</EntityName>
        <EntityType>tool</EntityType>
        <EntitySubType>malware</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
            <Label>TA0002</Label>
            <Label>TA0003</Label>
            <Label>TA0005</Label>
        </Labels>
        <Times>
            <Time>1</Time>
        </Times>
        <Properties>
            <Property name="type">Support module</Property>
            <Property name="target">Windows platform</Property>
            <Property name="versions">Windows NT 4.0 and later</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_3</EntityId>
        <EntityName>26297dc3cd0b688de3b846983c5385e5</EntityName>
        <EntityType>ioc</EntityType>
        <EntitySubType>hash</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
        </Labels>
        <Times>
            <Time>1</Time>
        </Times>
        <Properties>
            <Property name="type">MD5</Property>
            <Property name="category">Pure sample</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_4</EntityId>
        <EntityName>usbclass.sys</EntityName>
        <EntityType>file</EntityType>
        <EntitySubType>file</EntitySubType>
        <Labels>
            <Label>TA0005</Label>
        </Labels>
        <Times>
            <Time>1</Time>
        </Times>
        <Properties>
            <Property name="decoy">True</Property>
            <Property name="usage">44% of samples</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_5</EntityId>
        <EntityName>Extended Attributes</EntityName>
        <EntityType>tool</EntityType>
        <EntitySubType>method</EntitySubType>
        <Labels>
            <Label>TA0005</Label>
        </Labels>
        <Times>
            <Time>2</Time>
        </Times>
        <Properties>
            <Property name="storage">Payload storage</Property>
            <Property name="location">NTFS files/directories</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_6</EntityId>
        <EntityName>Windows Registry</EntityName>
        <EntityType>asset</EntityType>
        <EntitySubType>bussiness</EntitySubType>
        <Labels>
            <Label>TA0003</Label>
        </Labels>
        <Times>
            <Time>2</Time>
        </Times>
        <Properties>
            <Property name="usage">Fallback payload storage</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_7</EntityId>
        <EntityName>Trampoline Technique</EntityName>
        <EntityType>tool</EntityType>
        <EntitySubType>method</EntitySubType>
        <Labels>
            <Label>TA0005</Label>
        </Labels>
        <Times>
            <Time>3</Time>
        </Times>
        <Properties>
            <Property name="purpose">API call masking</Property>
            <Property name="sophistication">High</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_8</EntityId>
        <EntityName>Windows NT 5.2.3790</EntityName>
        <EntityType>env</EntityType>
        <EntitySubType>os</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
        </Labels>
        <Times>
            <Time>1</Time>
        </Times>
        <Properties>
            <Property name="version">Windows Server 2003</Property>
            <Property name="targeted">True</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_9</EntityId>
        <EntityName>NTOSKRNL.EXE</EntityName>
        <EntityType>file</EntityType>
        <EntitySubType>file</EntitySubType>
        <Labels>
            <Label>TA0005</Label>
        </Labels>
        <Times>
            <Time>3</Time>
        </Times>
        <Properties>
            <Property name="role">Trusted module for trampoline</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_10</EntityId>
        <EntityName>Regin Stage #2</EntityName>
        <EntityType>tool</EntityType>
        <EntitySubType>malware</EntitySubType>
        <Labels>
            <Label>TA0002</Label>
            <Label>TA0003</Label>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>4</Time>
        </Times>
        <Properties>
            <Property name="relationship">Loaded by Stage #1</Property>
        </Properties>
    </Entity>
</Entitys>
<Relationships>
    <Relationship>
        <RelationshipId>relationship_1</RelationshipId>
        <RelationshipType>use</RelationshipType>
        <Source>Regin</Source>
        <Target>W32/Regin Stage #1</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_2</RelationshipId>
        <RelationshipType>related_to</RelationshipType>
        <Source>W32/Regin Stage #1</Source>
        <Target>26297dc3cd0b688de3b846983c5385e5</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_3</RelationshipType>
        <RelationshipType>related_to</RelationshipType>
        <Source>W32/Regin Stage #1</Source>
        <Target>usbclass.sys</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_4</RelationshipId>
        <RelationshipType>use</RelationshipType>
        <Source>W32/Regin Stage #1</Source>
        <Target>Extended Attributes</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_5</RelationshipId>
        <RelationshipType>use</RelationshipType>
        <Source>W32/Regin Stage #1</Source>
        <Target>Windows Registry</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_6</RelationshipId>
        <RelationshipType>use</RelationshipType>
        <Source>W32/Regin Stage #1</Source>
        <Target>Trampoline Technique</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_7</RelationshipId>
        <RelationshipType>target</RelationshipType>
        <Source>W32/Regin Stage #1</Source>
        <Target>Windows NT 5.2.3790</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_8</RelationshipId>
        <RelationshipType>related_to</RelationshipType>
        <Source>Trampoline Technique</Source>
        <Target>NTOSKRNL.EXE</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_9</RelationshipId>
        <RelationshipType>related_to</RelationshipType>
        <Source>W32/Regin Stage #1</Source>
        <Target>Regin Stage #2</Target>
    </Relationship>
</Relationships>