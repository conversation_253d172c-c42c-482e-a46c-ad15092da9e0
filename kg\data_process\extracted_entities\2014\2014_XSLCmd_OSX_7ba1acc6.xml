<Entitys>
    <Entity>
        <EntityId>entity_1</EntityId>
        <EntityName>GREF</EntityName>
        <EntityVariantNames>
            <EntityVariantName>GREF Group</EntityVariantName>
        </EntityVariantNames>
        <EntityType>attcker</EntityType>
        <EntitySubType>org</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
            <Label>TA0002</Label>
            <Label>TA0003</Label>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>1</Time>
        </Times>
        <Properties>
            <Property name="country">China</Property>
            <Property name="activity_period">2009-present</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_2</EntityId>
        <EntityName>XSLCmd Backdoor Attack Event</EntityName>
        <EntityType>event</EntityType>
        <EntitySubType>event</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
            <Label>TA0002</Label>
            <Label>TA0003</Label>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>1</Time>
        </Times>
        <Properties>
            <Property name="discovery_date">August 2014</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_3</EntityId>
        <EntityName>OSX.XSLCmd</EntityName>
        <EntityType>tool</EntityType>
        <EntitySubType>malware</EntitySubType>
        <Labels>
            <Label>TA0002</Label>
            <Label>TA0003</Label>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>2</Time>
        </Times>
        <Properties>
            <Property name="hash">60242ad3e1b6c4d417d4dfeb8fb464a1</Property>
            <Property name="type">Mach-O executable</Property>
            <Property name="capabilities">reverse shell, file transfer, key logging, screen capture</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_4</EntityId>
        <EntityName>*************</EntityName>
        <EntityType>ioc</EntityType>
        <EntitySubType>ip</EntitySubType>
        <Labels>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>3</Time>
        </Times>
        <Properties>
            <Property name="role">C2 server</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_5</EntityId>
        <EntityName>www.appleupdate.biz</EntityName>
        <EntityType>ioc</EntityType>
        <EntitySubType>domain</EntitySubType>
        <Labels>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>3</Time>
        </Times>
        <Properties>
            <Property name="registration_date">2012-08-02</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_6</EntityId>
        <EntityName>OS X systems</EntityName>
        <EntityType>env</EntityType>
        <EntitySubType>os</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
            <Label>TA0002</Label>
        </Labels>
        <Times>
            <Time>2</Time>
        </Times>
        <Properties>
            <Property name="versions">10.8 and earlier</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_7</EntityId>
        <EntityName>US Defense Industrial Base</EntityName>
        <EntityType>vctim</EntityType>
        <EntitySubType>org</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
        </Labels>
        <Times>
            <Time>1</Time>
        </Times>
        <Properties>
            <Property name="industry">Defense</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_8</EntityId>
        <EntityName>Electronics and engineering companies</EntityName>
        <EntityType>vctim</EntityType>
        <EntitySubType>org</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
        </Labels>
        <Times>
            <Time>1</Time>
        </Times>
    </Entity>
    <Entity>
        <EntityId>entity_9</EntityId>
        <EntityName>Tibetan NGOs</EntityName>
        <EntityType>vctim</EntityType>
        <EntitySubType>org</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
        </Labels>
        <Times>
            <Time>1</Time>
        </Times>
    </Entity>
    <Entity>
        <EntityId>entity_10</EntityId>
        <EntityName>com.apple.service.clipboardd.plist</EntityName>
        <EntityType>file</EntityType>
        <EntitySubType>file</EntitySubType>
        <Labels>
            <Label>TA0003</Label>
        </Labels>
        <Times>
            <Time>2</Time>
        </Times>
        <Properties>
            <Property name="location">$HOME/Library/LaunchAgents/</Property>
            <Property name="purpose">Persistence mechanism</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_11</EntityId>
        <EntityName>pxupdate.ini</EntityName>
        <EntityType>file</EntityType>
        <EntitySubType>file</EntitySubType>
        <Labels>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>3</Time>
        </Times>
        <Properties>
            <Property name="location">$HOME/.fontset/</Property>
            <Property name="content">C2 configuration</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_12</EntityId>
        <EntityName>SQLMap</EntityName>
        <EntityType>tool</EntityType>
        <EntitySubType>tool</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
        </Labels>
        <Times>
            <Time>1</Time>
        </Times>
        <Properties>
            <Property name="type">SQL injection tool</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_13</EntityId>
        <EntityName>Acunetix</EntityName>
        <EntityType>tool</EntityType>
        <EntitySubType>tool</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
        </Labels>
        <Times>
            <Time>1</Time>
        </Times>
        <Properties>
            <Property name="type">Web vulnerability scanner</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_14</EntityId>
        <EntityName>Poison Ivy</EntityName>
        <EntityType>tool</EntityType>
        <EntitySubType>malware</EntitySubType>
        <Labels>
            <Label>TA0002</Label>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>1</Time>
        </Times>
    </Entity>
    <Entity>
        <EntityId>entity_15</EntityId>
        <EntityName>Gh0st</EntityName>
        <EntityType>tool</EntityType>
        <EntitySubType>malware</EntitySubType>
        <Labels>
            <Label>TA0002</Label>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>1</Time>
        </Times>
    </Entity>
    <Entity>
        <EntityId>entity_16</EntityId>
        <EntityName>Kaba/SOGU</EntityName>
        <EntityType>tool</EntityType>
        <EntitySubType>malware</EntitySubType>
        <Labels>
            <Label>TA0002</Label>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>1</Time>
        </Times>
    </Entity>
</Entitys>
<Relationships>
    <Relationship>
        <RelationshipId>relationship_1</RelationshipId>
        <RelationshipType>involve</RelationshipType>
        <Source>XSLCmd Backdoor Attack Event</Source>
        <Target>GREF</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_2</RelationshipId>
        <RelationshipType>use</RelationshipType>
        <Source>GREF</Source>
        <Target>OSX.XSLCmd</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_3</RelationshipId>
        <RelationshipType>target</RelationshipType>
        <Source>GREF</Source>
        <Target>US Defense Industrial Base</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_4</RelationshipId>
        <RelationshipType>target</RelationshipType>
        <Source>GREF</Source>
        <Target>Electronics and engineering companies</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_5</RelationshipId>
        <RelationshipType>target</RelationshipType>
        <Source>GREF</Source>
        <Target>Tibetan NGOs</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_6</RelationshipId>
        <RelationshipType>related_to</RelationshipType>
        <Source>OSX.XSLCmd</Source>
        <Target>*************</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_7</RelationshipId>
        <RelationshipType>related_to</RelationshipType>
        <Source>OSX.XSLCmd</Source>
        <Target>www.appleupdate.biz</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_8</RelationshipId>
        <RelationshipType>affect</RelationshipType>
        <Source>OSX.XSLCmd</Source>
        <Target>OS X systems</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_9</RelationshipId>
        <RelationshipType>belong_to</RelationshipType>
        <Source>com.apple.service.clipboardd.plist</Source>
        <Target>OS X systems</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_10</RelationshipId>
        <RelationshipType>belong_to</RelationshipType>
        <Source>pxupdate.ini</Source>
        <Target>OS X systems</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_11</RelationshipId>
        <RelationshipType>use</RelationshipType>
        <Source>GREF</Source>
        <Target>SQLMap</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_12</RelationshipId>
        <RelationshipType>use</RelationshipType>
        <Source>GREF</Source>
        <Target>Acunetix</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_13</RelationshipId>
        <RelationshipType>use</RelationshipType>
        <Source>GREF</Source>
        <Target>Poison Ivy</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_14</RelationshipId>
        <RelationshipType>use</RelationshipType>
        <Source>GREF</Source>
        <Target>Gh0st</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_15</RelationshipId>
        <RelationshipType>use</RelationshipType>
        <Source>GREF</Source>
        <Target>Kaba/SOGU</Target>
    </Relationship>
</Relationships>