[{"Filename": "WickedRose_andNCPH", "Title": "\"Wicked Rose\" And The Ncph Hacking Group", "Source": "iDefense", "Link": "https://app.box.com/s/********************************", "SHA-1": "e802d6f06085f22242a66b06c785315e1c63c070", "Date": "12/01/2006", "Year": "2006"}, {"Filename": "Fritz_HOW-CHINA-WILL-USE-CYBER-WARFARE(Oct-01-08)", "Title": "How China Will Use Cyber Warfare", "Source": "<PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "3e6399a4b608bbd99dd81bd2be4cd49731362b5e", "Date": "10/01/2008", "Year": "2008"}, {"Filename": "556_10535_798405_Annex87_CyberAttacks", "Title": "Russian Cyberwar On Georgia", "Source": "Georgia Gov", "Link": "https://app.box.com/s/********************************", "SHA-1": "2fbd7813367fad45e7fd1922381a05e27b0e9673", "Date": "11/11/2008", "Year": "2008"}, {"Filename": "Ashmore_Impact-of-Alleged-Russian-Cyber-Attacks(Jan-18-09)", "Title": "Impact Of Alleged Russian Cyber Attack", "Source": "<PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "b0f5f77865f24c5064f78ed7cd30bbdf2c111945", "Date": "01/18/2009", "Year": "2009"}, {"Filename": "ghostnet", "Title": "Tracking Ghostnet: Investigating A Cyber Espionage Network", "Source": "Information Warfare Monitor", "Link": "https://app.box.com/s/********************************", "SHA-1": "28dd92f598e7d8987d8236767856c70be4f7e85f", "Date": "03/29/2009", "Year": "2009"}, {"Filename": "Case_Study_Operation_Aurora_V11", "Title": "Case Study: Operation Aurora", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "510f1b2342f3ca3dd24179821eb451488d6e9722", "Date": "01/01/2010", "Year": "2010"}, {"Filename": "Aurora_Botnet_Command_Structure", "Title": "The Command Structure Of The Aurora Botnet", "Source": "<PERSON><PERSON>a", "Link": "https://app.box.com/s/********************************", "SHA-1": "9a7a1d3f7719c02dced8633ac0bf43d6e9ec7a1b", "Date": "01/13/2010", "Year": "2010"}, {"Filename": "McAfee_Operation_Aurora", "Title": "Combating Aurora", "Source": "McAfee", "Link": "https://app.box.com/s/********************************", "SHA-1": "52123660be8b8cad9d46244f669f2cfd96101750", "Date": "01/20/2010", "Year": "2010"}, {"Filename": "Aurora_HBGARY_DRAFT", "Title": "Operation Aurora: <PERSON><PERSON><PERSON>, <PERSON><PERSON>nose, Re<PERSON>ond", "Source": "HBGary", "Link": "https://app.box.com/s/********************************", "SHA-1": "3494bd514034f0658ec66b2f515cd9a60c51a8e7", "Date": "01/27/2010", "Year": "2010"}, {"Filename": "HBGary_Operation_Aurora", "Title": "Operation Aurora", "Source": "HBGary", "Link": "https://app.box.com/s/********************************", "SHA-1": "4d9c8ecae38f217729cf8a9df934e7cc5de2ae1a", "Date": "02/10/2010", "Year": "2010"}, {"Filename": "how_can_u_tell_<PERSON>", "Title": "How Can I Tell If I Was Infected By Aurora?", "Source": "McAfee", "Link": "https://app.box.com/s/********************************", "SHA-1": "1001e5c45200e0f138e9e9d508afc31c475d6ce7", "Date": "02/24/2010", "Year": "2010"}, {"Filename": "in-depth_analysis_of_hydraq_final_231538", "Title": "In-Depth Analysis Of Hydraq: The Face Of Cyberwar Enemies Unfolds", "Source": "CA", "Link": "https://app.box.com/s/********************************", "SHA-1": "6eb3094ecab54a8b80932f4bec263696f849ca77", "Date": "03/14/2010", "Year": "2010"}, {"Filename": "Shadowserver_shadows-in-the-cloud", "Title": "Shadows In The Cloud: Investigating Cyber Espionage 2.0", "Source": "Shadowserver, Information warfare monitor", "Link": "https://app.box.com/s/********************************", "SHA-1": "8a982bc5c8303440faa4d5672a38bb7a613c382b", "Date": "04/06/2010", "Year": "2010"}, {"Filename": "WashingtonPost_2010-Defense-official-discloses-cyberattack(08-24-2010)", "Title": "Defense official discloses cyberattack", "Source": "Washington Post", "Link": "https://app.box.com/s/********************************", "SHA-1": "9b22d8de05493cd184246d30a4691005e9a30b94", "Date": "08/24/2010", "Year": "2010"}, {"Filename": "MSUpdaterT<PERSON>jan<PERSON>", "Title": "The Msupdater Trojan And Ongoing Targeted Attacks ", "Source": "<PERSON><PERSON><PERSON>, Zscaler", "Link": "https://app.box.com/s/********************************", "SHA-1": "c7d0387067ba747e3a3d9b43b7349d7888bf574e", "Date": "09/03/2010", "Year": "2010"}, {"Filename": "w32_stuxnet_dossier", "Title": "W32.Stuxnet Dossier", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "93c9ad9c9d9e1d882d56d8ceb1aa684d147d0a78", "Date": "02/01/2011", "Year": "2011"}, {"Filename": "wp-global-energy-cyberattacks-night-dragon", "Title": "Global Energy Cyberattacks: Night Dragon", "Source": "McAfee", "Link": "https://app.box.com/s/********************************", "SHA-1": "e0fce95ccdb9c400f2dd3859ebe268f5bc7877ce", "Date": "02/10/2011", "Year": "2011"}, {"Filename": "Alerts DL-2011 Alerts-A-2011-02-18-01 Night Dragon Attachment 1", "Title": "Night Dragon: Specific Protection Measures For Consideration ", "Source": "NERC", "Link": "https://app.box.com/s/********************************", "SHA-1": "1fe534fe68fe1a93ef2b536f1365219653b560ee", "Date": "02/18/2011", "Year": "2011"}, {"Filename": "Stuxnet_Under_the_Microscope", "Title": "Stuxnet Under The Microscope", "Source": "ESET", "Link": "https://app.box.com/s/********************************", "SHA-1": "4c1b1b0da537c482d359bf75435cb8abb1df7093", "Date": "04/20/2011", "Year": "2011"}, {"Filename": "C5_APT_ADecadeInReview", "Title": "Advanced Persistent Threats: A Decade In Review", "Source": "Command Five Pty Ltd", "Link": "https://app.box.com/s/********************************", "SHA-1": "725568c41fa9f1d7e8a2226e71e5b2a39fd08121", "Date": "06/01/2011", "Year": "2011"}, {"Filename": "shady_rat_vanity", "Title": "Operation Shady Rat: Unprecedented Cyber-Espionage Campaign And Intellectual-Property Bonanza", "Source": "Vanity Fair", "Link": "https://app.box.com/s/********************************", "SHA-1": "254132938c6ff6eeaa5e3b1e8d8d506472c028b7", "Date": "08/02/2011", "Year": "2011"}, {"Filename": "HTran_and_the_Advanced_Persistent_Threat", "Title": "Htran And The Advanced Persistent Threat", "Source": "Dell Secureworks", "Link": "https://app.box.com/s/********************************", "SHA-1": "1461452398e57d541209eb6bc29e0743369b373b", "Date": "08/03/2011", "Year": "2011"}, {"Filename": "wp-operation-shady-rat", "Title": "Revealed: <PERSON> <PERSON>hady Rat", "Source": "McAfee", "Link": "https://app.box.com/s/********************************", "SHA-1": "deb92a55dffa951697d3367750df2cf8e4480f8f", "Date": "08/04/2011", "Year": "2011"}, {"Filename": "wp_dissecting-lurid-apt", "Title": "The Lurid Downloader", "Source": "Trend Micro", "Link": "https://app.box.com/s/********************************", "SHA-1": "23f1f9e5771be71725fa19487da59f6779f5ee3f", "Date": "08/22/2011", "Year": "2011"}, {"Filename": "C5_APT_SKHack", "Title": "Sk Hack By An Advanced Persistent Threat", "Source": "Command Five Pty Ltd", "Link": "https://app.box.com/s/********************************", "SHA-1": "2b98220caf158d1c4f6d72abbc379899e35edc4d", "Date": "09/11/2011", "Year": "2011"}, {"Filename": "tb_advanced_persistent_threats", "Title": "Alleged Apt Intrusion Set: 1.Php Group", "Source": "<PERSON><PERSON><PERSON>, ThreatLabz", "Link": "https://app.box.com/s/********************************", "SHA-1": "fd81d98729029a483e0c5c69d908ab96014edbe5", "Date": "10/12/2011", "Year": "2011"}, {"Filename": "Duqu_Trojan_Questions_and_Answers", "Title": "<PERSON><PERSON>u Trojan Questions And Answers", "Source": "Dell Secureworks", "Link": "https://app.box.com/s/********************************", "SHA-1": "c59b324139b965677a9933f7435b5ac34ca40126", "Date": "10/26/2011", "Year": "2011"}, {"Filename": "the_nitro_attacks", "Title": "The Nitro Attacks: Stealing Secrets From The Chemical Industry", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "f26ddf5c622dd52fc46cd46813c9552a08214587", "Date": "10/31/2011", "Year": "2011"}, {"Filename": "<PERSON><PERSON><PERSON>_Palestinian_credentials", "Title": "Palebot Trojan Harvests Palestinian Online Credentials", "Source": "<PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "b0ac3fce955bb8361b98a791919d639b18877d56", "Date": "12/08/2011", "Year": "2011"}, {"Filename": "WashingtonPost_2011-Cyber-intruder-sparks-response-debate(12-08-2011)", "Title": "Cyber-intruder sparks response, debate", "Source": "Washington Post", "Link": "https://app.box.com/s/********************************", "SHA-1": "8fcaa75fb993d568f7dda7f9f237a27811839420", "Date": "12/08/2011", "Year": "2011"}, {"Filename": "Evolution_Drivers_Duqu_Stuxnet", "Title": "Stuxnet/Duqu: The Evolution Of Drivers", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "f4231f388207b578c46b126e14d5ed4f9b405424", "Date": "12/28/2011", "Year": "2011"}, {"Filename": "wp_the-heartbeat-apt-campaign", "Title": "The Heartbeat Apt Campaign", "Source": "Trend Micro", "Link": "https://app.box.com/s/********************************", "SHA-1": "a486fb5b0b485796f0b2d1248c948e7c64c6b49a", "Date": "01/03/2012", "Year": "2012"}, {"Filename": "The_Sin_Digoo_Affair", "Title": "The Sin Digoo Affair", "Source": "Dell Secureworks", "Link": "https://app.box.com/s/********************************", "SHA-1": "771d703ce533bea8dbece799705ce8e61717a8a5", "Date": "02/29/2012", "Year": "2012"}, {"Filename": "Crouching_tiger_hidden_dragon", "Title": "Crouching <PERSON>, <PERSON> Dragon, <PERSON><PERSON><PERSON>", "Source": "Contextis", "Link": "https://app.box.com/s/********************************", "SHA-1": "75c240fb4334b2307b56c336284acad112e40063", "Date": "03/12/2012", "Year": "2012"}, {"Filename": "Crypto-DarkComet-Report", "Title": "It'S Not The End Of The World: Darkcomet Misses By A Mile", "Source": "Arbor Networks", "Link": "https://app.box.com/s/********************************", "SHA-1": "50511389957f7c2e0127031d8633724ae05354f3", "Date": "03/13/2012", "Year": "2012"}, {"Filename": "wp_luckycat_redux", "Title": "Luckycat Redux: Inside An Apt Campaign With Multiple Targets  In India And Japan", "Source": "Trend Micro", "Link": "https://app.box.com/s/********************************", "SHA-1": "bdb218fdc0cce0bc57f77a9a1e6d3cc7e81b55f9", "Date": "03/26/2012", "Year": "2012"}, {"Filename": "Symantec_The-luckycat-hackers(04-03-2012)", "Title": "The Luckycat Hackers", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "****************************************", "Date": "04/03/2012", "Year": "2012"}, {"Filename": "OSX_SabPub", "Title": "New Version Of Osx.Sabpub & Confirmed Mac Apt Attacks", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "0b792ff94ca71eeb71aba780286f7c4bf9f75b40", "Date": "04/16/2012", "Year": "2012"}, {"Filename": "w32_flamer_newsforyou", "Title": "Have I Got Newsforyou: Analysis Of Flamer C&C Server", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "0120f4f065c62bcd218bbc21bc729bd3645adf14", "Date": "05/18/2012", "Year": "2012"}, {"Filename": "wp_ixeshe", "Title": "Ixeshe An Apt Campaign", "Source": "Trend Micro", "Link": "https://app.box.com/s/********************************", "SHA-1": "c00b7449b733f070c148c5b6c0a4df087a3f34f1", "Date": "05/22/2012", "Year": "2012"}, {"Filename": "skywiper", "Title": "Skywiper (A.K.A. Flame A.K.A. Flamer): A Complex Malware For Targeted Attacks ", "Source": "CrySyS, BME", "Link": "https://app.box.com/s/********************************", "SHA-1": "6e4df95a65ad848c8192c7c76ed35d622764cab3", "Date": "05/31/2012", "Year": "2012"}, {"Filename": "PEST-CONTROL", "Title": "Pest Control: Taming The Rats", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "203258819d7fbffdfae2c5df96d71254a2816ca6", "Date": "06/13/2012", "Year": "2012"}, {"Filename": "Tibet_Lurk", "Title": "Recent Observations In Tibet-Related Information Operations: Advanced Social Engineering For The Distribution Of Lurk Malware", "Source": "Citizen Lab", "Link": "https://app.box.com/s/********************************", "SHA-1": "61c047476369c5069e39f3d06825d847d924d216", "Date": "07/10/2012", "Year": "2012"}, {"Filename": "From-Bahrain-With-Love-FinFishers-Spy-Kit-Exposed", "Title": "From Bahrain With Love: <PERSON><PERSON>er Spy Kit Exposed?", "Source": "Citizen Lab", "Link": "https://app.box.com/s/********************************", "SHA-1": "6d0de3e44a012f45a7b56a9862d1d67ef8bfd7e8", "Date": "07/25/2012", "Year": "2012"}, {"Filename": "The_Madi_Infostealers", "Title": "The 'Madi' Infostealers - A Detailed Analysis", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "51b16ee4bb04d663a4c67e23e3d3bf816ae12207", "Date": "07/27/2012", "Year": "2012"}, {"Filename": "kaspersky-lab-gauss", "Title": "Gauss: Abnormal Distribution", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "4c9c37199b1e1da37d0dae56f7a6469e0b0a4c6e", "Date": "08/09/2012", "Year": "2012"}, {"Filename": "VOHO_WP_FINAL_READY-FOR-Publication-09242012_AC", "Title": "The Voho Campaign: An In Depth Analysis ", "Source": "RSA", "Link": "https://app.box.com/s/********************************", "SHA-1": "759e45d0b495ec23949b3324fd543df7e450afdc", "Date": "08/12/2012", "Year": "2012"}, {"Filename": "The_Mirage_Campaign", "Title": "The Mirage Campaign", "Source": "Dell Secureworks", "Link": "https://app.box.com/s/********************************", "SHA-1": "cfd4451a15223fdf667285b12a8829a4b409495a", "Date": "08/18/2012", "Year": "2012"}, {"Filename": "the-elderwood-project", "Title": "The Elderwood Project", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "ce7a662c0f822536e6beff4856f701d28137c8e1", "Date": "09/06/2012", "Year": "2012"}, {"Filename": "IEXPL0RE_RAT", "Title": "Iexpl0Re Rat", "Source": "Citizen Lab", "Link": "https://app.box.com/s/********************************", "SHA-1": "bd6f3f93d3c87e78149d766b2613ed9e18bc2620", "Date": "09/07/2012", "Year": "2012"}, {"Filename": "trojan_taidoor-targeting_think_tanks", "Title": "Trojan.Taidoor: Targeting Think Tanks", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "c32627e18128fbf249a084272310996d1b794bb5", "Date": "10/27/2012", "Year": "2012"}, {"Filename": "FTA 1007 - Shamoon", "Title": "Recovering From Shamoon", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "4477f8bb9e82fa99d4c0f1d57720f5856b8ee9f8", "Date": "11/01/2012", "Year": "2012"}, {"Filename": "Cyberattack_against_Israeli_and_Palestinian_targets", "Title": "Systematic Cyber Attacks Against Israeli And Palestinian Targets Going On For A Year  ", "Source": "<PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "299a326641766c588a04f990927f795ecc31726f", "Date": "11/03/2012", "Year": "2012"}, {"Filename": "Faces_Ghost_RAT", "Title": "The Many Faces Of Gh0St Rat: Plotting The Connections Between Malware Attacks", "Source": "<PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "02e2959da1d4522c2d94ffbc7a1871470b2f7912", "Date": "11/30/2012", "Year": "2012"}, {"Filename": "Securelist_RedOctober", "Title": "The \"Red October\" Campaign - An Advanced Cyber Espionage Network Targeting Diplomatic And Government Agencies", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "b64a571a29d1c2cfc40b6b6cef50a95e6ce3b455", "Date": "01/14/2013", "Year": "2013"}, {"Filename": "Securelist_RedOctober_Detail", "Title": "\"Red October\" Diplomatic Cyber Attacks Investigation", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "f9e75ac3b51ec2dd195a2fd82743530f9534dd40", "Date": "01/14/2013", "Year": "2013"}, {"Filename": "icefog", "Title": "The Icefog Apt: A Tale Of Cloak And Three Daggers", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "751d00dab0fac4c2b0d1c140e9897fb99a399be1", "Date": "01/14/2013", "Year": "2013"}, {"Filename": "McAfee_Labs_Threat_Advisory_Exploit_Operation_Red_Oct", "Title": "Operation Red October", "Source": "McAfee", "Link": "https://app.box.com/s/********************************", "SHA-1": "b6217ea7fe6b4dd5e27b2a1b4b84432db2e1b2f3", "Date": "01/18/2013", "Year": "2013"}, {"Filename": "FireEye_Operation-Beebus(Feb-1-13)", "Title": "Operation Beebus", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "829bd809578a0305bc0438fd719e9c6da8f9fd96", "Date": "02/01/2013", "Year": "2013"}, {"Filename": "C5_APT_C2InTheFifthDomain", "Title": "Command And Control In The Fifth Domain", "Source": "Command Five Pty Ltd", "Link": "https://app.box.com/s/********************************", "SHA-1": "f8b1d371008a2108bb7ded054b7b0b7cdc4d5295", "Date": "02/03/2013", "Year": "2013"}, {"Filename": "Presentation_Targeted-Attacks_EN", "Title": "Targeted Cyber Attacks:  Examples And Challenges Ahead", "Source": "CrySyS", "Link": "https://app.box.com/s/********************************", "SHA-1": "100df21fed6fcf08b0982cfdf55463608613a2e2", "Date": "02/12/2013", "Year": "2013"}, {"Filename": "Mandiant_APT1_Report", "Title": "Apt1 Exposing One Of China's Cyber Espionage Units", "Source": "Mandiant", "Link": "https://app.box.com/s/********************************", "SHA-1": "3974687624eb85cdcf1fc9ccfb68eea052971e84", "Date": "02/18/2013", "Year": "2013"}, {"Filename": "comment_crew_indicators_of_compromise", "Title": "Comment Crew: Indicators Of Compromise", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "349984643361273d5d3b638e369c45cbb409676c", "Date": "02/22/2013", "Year": "2013"}, {"Filename": "stuxnet_0_5_the_missing_link", "Title": "Stuxnet 0.5: The Missing Link", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "712311f4cacd476100c7ef037e3accc740558920", "Date": "02/26/2013", "Year": "2013"}, {"Filename": "themysteryofthepdf0-dayassemblermicrobackdoor", "Title": "The Miniduke Mystery: Pdf 0-Day Government Spy Assembler 0X29A Micro Backdoor ", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "c6dff7f0864e36e3dcc1be12747d26fb8072b52c", "Date": "02/27/2013", "Year": "2013"}, {"Filename": "miniduke_indicators_public", "Title": "Miniduke: Indicators ", "Source": "CrySyS, BME", "Link": "https://app.box.com/s/********************************", "SHA-1": "2d3fb67fd870f192c38bd8e51344d45645794623", "Date": "02/27/2013", "Year": "2013"}, {"Filename": "15-2013-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Title": "You Only Click Twice: Finfisher's Global Proliferation", "Source": "Citizen Lab", "Link": "https://app.box.com/s/********************************", "SHA-1": "e33abbd24c9cbb57b3b4a97df165766e1fb42eeb", "Date": "03/13/2013", "Year": "2013"}, {"Filename": "Safe-a-targeted-threat", "Title": "Safe A Targeted Threat", "Source": "Trend Micro", "Link": "https://app.box.com/s/********************************", "SHA-1": "334b4ee90a30c9ab9dcc6e3596f15f0dcb02486d", "Date": "03/17/2013", "Year": "2013"}, {"Filename": "theteamspystory_final_t2", "Title": "The Teamspy Story - Abusing Teamviewer In Cyberespionage Campaigns", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "57a17587785f54a103ea970277bd7c4cc179a82c", "Date": "03/20/2013", "Year": "2013"}, {"Filename": "dissecting-operation-troy", "Title": "Dissecting Operation Troy: Cyberespionage In South Korea", "Source": "McAfee", "Link": "https://app.box.com/s/********************************", "SHA-1": "c6ce890a544b01e5dafd4add9326b4178424c4e2", "Date": "03/20/2013", "Year": "2013"}, {"Filename": "RAP002_APT1_Technical_backstage.1.0", "Title": "Apt1: Technical Backstage", "Source": "Malware.lu, itrust", "Link": "https://app.box.com/s/********************************", "SHA-1": "26353a7703ce0b186450134a5321ac37d1405380", "Date": "03/27/2013", "Year": "2013"}, {"Filename": "tr-12-circl-plugx-analysis-v1", "Title": "Analysis Of A Plugx Variant (Plugx Version 7.0)", "Source": "CIRCL", "Link": "https://app.box.com/s/********************************", "SHA-1": "875abf02dad2a434d708e495ffc8afe4b2500aae", "Date": "03/28/2013", "Year": "2013"}, {"Filename": "Trojan.APT.BaneChant", "Title": "Trojan.Apt.Banechant: In-Memory Trojan That Observes For Multiple Mouse Clicks", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "85ef5daf99603da833a32245fd120028829a666f", "Date": "04/01/2013", "Year": "2013"}, {"Filename": "MiniDuke_Paper_Final", "Title": "A Closer Look At Miniduke", "Source": "Bitdefender", "Link": "https://app.box.com/s/********************************", "SHA-1": "d88b186085918e2039514caa21a4017aafc556d1", "Date": "04/03/2013", "Year": "2013"}, {"Filename": "winnti-more-than-just-a-game-130410", "Title": "Winnti: More Than Just A Game", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "3a34d6152f2d287f58e67a03d96408b74d5c470e", "Date": "04/13/2013", "Year": "2013"}, {"Filename": "FireEye_The-Mutter-Backdoor-Operation-Beebus-with-New-Targets(Apr-17-13)", "Title": "The Mutter Backdoor: Operation Beebus with New Targets", "Source": "Fireeye", "Link": "https://app.box.com/s/********************************", "SHA-1": "660bd04b0d4b33301fcb47a98f3d671f126f66c0", "Date": "04/17/2013", "Year": "2013"}, {"Filename": "AdversaryIntelligenceReport_DeepPanda_0 (1)", "Title": "Deep Panda", "Source": "Crowdstrike", "Link": "https://app.box.com/s/********************************", "SHA-1": "1d53861aafea11d9a60e798b90d623c8e7c7b9e7", "Date": "05/03/2013", "Year": "2013"}, {"Filename": "NS-Unveiling-an-Indian-Cyberattack-Infrastructure_FINAL_Web", "Title": "Operation Hangover - Unveiling An Indian Cyberattack Infrastructure", "Source": "<PERSON>, Shadows<PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "aae01ca44ca11f33692bcfd9a20e36105ddfa2b9", "Date": "05/20/2013", "Year": "2013"}, {"Filename": "<PERSON><PERSON>HangOver report_Executive Summary_042513", "Title": "Operation Hangover |Executive Summary", "Source": "<PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "e2631efe178d16691329f27a853a41a48387bfc4", "Date": "05/20/2013", "Year": "2013"}, {"Filename": "circl-analysisreport-miniduke-stage3-public", "Title": "Analysis Of A Stage 3 Miniduke Sample", "Source": "CIRCL", "Link": "https://app.box.com/s/********************************", "SHA-1": "dd9dacb6b9bc34a2410f9c8fb3a5f04fdce77f29", "Date": "05/30/2013", "Year": "2013"}, {"Filename": "NormanShark-MaudiOperation", "Title": "The Chinese Malware Complexes: The Maudi Surveillance Operation", "Source": "<PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "902c5b2eb9cb486171b24ca60681aea5eecdb270", "Date": "06/01/2013", "Year": "2013"}, {"Filename": "2013-9", "Title": "<PERSON><PERSON><PERSON>: An Analysis Of Cyber Conflict Within The Oil & Gas Industries", "Source": "CERIAS", "Link": "https://app.box.com/s/********************************", "SHA-1": "31d750b3565b65c43533a87bf7aa72c41258f9a0", "Date": "06/01/2013", "Year": "2013"}, {"Filename": "kaspersky-the-net-traveler-part1-final", "Title": "The Nettraveler  (Aka Travnet) ", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "4fd06d33aa1a087709277eb71d204e2fbf8b9243", "Date": "06/04/2013", "Year": "2013"}, {"Filename": "KeyBoy_Vietnam_India", "Title": "Keyboy, Targeted Attacks Against Vietnam And India", "Source": "Rapid7", "Link": "https://app.box.com/s/********************************", "SHA-1": "9a2de0730e62aa24cdff1d1920b9535d1795be7e", "Date": "06/07/2013", "Year": "2013"}, {"Filename": "Trojan.APT<PERSON>", "Title": "Trojan.A<PERSON>.<PERSON><PERSON><PERSON>", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "c6e80e76bca03538a2dfd2aac2e4120fdab6f4fc", "Date": "06/18/2013", "Year": "2013"}, {"Filename": "19-2013-a<PERSON><PERSON><PERSON><PERSON>", "Title": "A Call To Harm: New Malware Attacks Target The Syrian Opposition", "Source": "Citizen Lab", "Link": "https://app.box.com/s/********************************", "SHA-1": "283e853a69cddd4b59b35fcc2a75205b1deb9b69", "Date": "06/21/2013", "Year": "2013"}, {"Filename": "fta-1009---njrat-uncovered-1", "Title": "<PERSON><PERSON><PERSON> Uncovered", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "214daebe12ae36a91e13c83e820bb2e20b560828", "Date": "06/28/2013", "Year": "2013"}, {"Filename": "US-13-Ya<PERSON>chkin-In-Depth-Analysis-of-Escalated-APT-Attacks-Slides", "Title": "Hunting The Shadows: In Depth Analysis Of Escalated Apt Attacks", "Source": "Xecure, Academia Sinica", "Link": "https://app.box.com/s/********************************", "SHA-1": "05f1beceab73155d552a5ee919aebc4c8e92c609", "Date": "07/01/2013", "Year": "2013"}, {"Filename": "Dark_Seoul_Cyberattack", "Title": "Dark Seoul Cyber Attack: Could It Be Worse? ", "Source": "Dongseo University", "Link": "https://app.box.com/s/********************************", "SHA-1": "4e6876f16b77dbb33aad5384dbf0549d717edb31", "Date": "07/09/2013", "Year": "2013"}, {"Filename": "Plugx_Smoaler", "Title": "The Plugx Malware Revisited: Introducing Smoaler", "Source": "<PERSON>ph<PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "9a7978041e795b788d86c52a477160dae296413b", "Date": "07/15/2013", "Year": "2013"}, {"Filename": "Secrets_of_the_Comfoo_Masters", "Title": "Secrets Of The Comfoo Masters", "Source": "Dell Secureworks", "Link": "https://app.box.com/s/********************************", "SHA-1": "86061f7d1994ebb7b8b7eb640b041fb1342adac5", "Date": "07/31/2013", "Year": "2013"}, {"Filename": "Unveiling an Indian Cyberattack Infrastructure - appendixes", "Title": "Operation Hangover - Unveiling An Indian Cyberattack Infrastructure (Appendix)", "Source": "<PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "ffdfed40c5b1e08a6469c2f38e6a51347a37dd1b", "Date": "08/01/2013", "Year": "2013"}, {"Filename": "Inside_Report_by_Infosec_Consortium", "Title": "Inside Report _ Apt Attacks On Indian Cyber Space", "Source": "Infosec Consortium", "Link": "https://app.box.com/s/********************************", "SHA-1": "886b0ab831a5084de7d80355b7578a9a9999fc88", "Date": "08/01/2013", "Year": "2013"}, {"Filename": "Surtr_Malware_Tibetan", "Title": "Surtr: Malware Family Targeting The Tibetan Community", "Source": "Citizen Lab", "Link": "https://app.box.com/s/********************************", "SHA-1": "6b624d6d80b412a10260344f244ed93e3718e003", "Date": "08/02/2013", "Year": "2013"}, {"Filename": "India_Pak_Tranchulas", "Title": "Where There Is Smoke, There Is Fire: South Asian Cyber Espionage Heats Up", "Source": "ThreatConnect", "Link": "https://app.box.com/s/********************************", "SHA-1": "665c6ace0a9175735f6cdb656e79287f5a53e072", "Date": "08/02/2013", "Year": "2013"}, {"Filename": "fireeye-china-chopper-report", "Title": "The Little Malware That Could: Detecting And Defeating The China Chopper Web Shell", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "8a3a657ac02569c1324ade4cca562ae8c5781f94", "Date": "08/07/2013", "Year": "2013"}, {"Filename": "NYTimes_Attackers_Evolve_Quickly", "Title": "Survival Of The Fittest: New York Times Attackers Evolve Quickly", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "5f17e7b886d2388ffc134157dd1b66aa65372b59", "Date": "08/12/2013", "Year": "2013"}, {"Filename": "ByeBye_Shell_target", "Title": "Byebye Shell And The Targeting Of Pakistan", "Source": "Rapid7", "Link": "https://app.box.com/s/********************************", "SHA-1": "4b6cc1420b5d641564c9895882a5ba97d18137be", "Date": "08/19/2013", "Year": "2013"}, {"Filename": "fireeye-poison-ivy-report", "Title": "Poison Ivy:  Assessing Damage And Extracting Intelligence", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "5276375276d632b9a43037ef9a99221b81cb1a61", "Date": "08/21/2013", "Year": "2013"}, {"Filename": "Operation_Molerats", "Title": "Operation Molerats", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "3b74caac2fb42068781f9812b5080a6975ce3d24", "Date": "08/23/2013", "Year": "2013"}, {"Filename": "Operation_EphemeralHydra", "Title": "Operation Ephemeral Hydra: Ie Zero-Day Linked To Deputydog Uses Diskless Method", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "1bf6c5eaaf996f463b25837c15b400c895862419", "Date": "09/10/2013", "Year": "2013"}, {"Filename": "<PERSON><PERSON><PERSON>", "Title": "The \"Kimsuky\" Operation: A North Korean Apt?", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "64f98d4c515e31f68c6e7fdf442753a6af8c0bee", "Date": "09/11/2013", "Year": "2013"}, {"Filename": "Operation_DeputyDog", "Title": "Operation Deputydog: Zero-<PERSON> (Cve-2013-3893) Attack Against Japanese Targets", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "21a5b79498a242f6869649cb808d02366dba6e89", "Date": "09/13/2013", "Year": "2013"}, {"Filename": "hidden_lynx", "Title": "Hidden Lynx: Professional Hackers For Hire", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "e4aa40ce1a865021e0b178a4c444126743335d32", "Date": "09/17/2013", "Year": "2013"}, {"Filename": "2q-report-on-targeted-attack-campaigns", "Title": "2Q Report On Targeted Attack Campaigns", "Source": "Trend Micro", "Link": "https://app.box.com/s/********************************", "SHA-1": "f69e4d23674d06ee459d2abbecc5f3f4cbd58047", "Date": "09/19/2013", "Year": "2013"}, {"Filename": "fireeye-wwc-report", "Title": "World War C: Understanding Nation-State Motives Behind Today's Advanced Cyber Attacks", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "5ff5d49650b4fa5988f435f60434744a7bcaee48", "Date": "09/30/2013", "Year": "2013"}, {"Filename": "wp-fakem-rat", "Title": "Fakem Rat: Malware Disguised As Windows Messenger And Yahoo! Messenger", "Source": "Trend Micro", "Link": "https://app.box.com/s/********************************", "SHA-1": "a899d6713da1b9da8aa77cb3db5360b4e8574b5c", "Date": "10/24/2013", "Year": "2013"}, {"Filename": "FireEye-Terminator_RAT", "Title": "Evasive Tactics: Terminator Rat", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "9a4dcfa7178cccc6283a92bc768be1374eb347f9", "Date": "10/24/2013", "Year": "2013"}, {"Filename": "fireeye-malware-supply-chain", "Title": "Supply Chain Analysis: From Quartermaster To Sunshopfireeye", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "7ddab1f54ef0ba0a48efdb7ca39ddcc82f0a84d6", "Date": "11/11/2013", "Year": "2013"}, {"Filename": "FTA 1010 - njRAT The Saga Continues", "Title": "\"Nj<PERSON>\", The Saga Continues", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "a8f9bc957ae0ee7e38a2cefb83afff0b1bd68422", "Date": "12/02/2013", "Year": "2013"}, {"Filename": "fireeye-operation-ke3chang", "Title": "Operation Ke3Chang Targeted Attacks Against Ministries Of Foreign Affairs", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "b670b7a7904baded891a4c6e7afe692998989668", "Date": "12/11/2013", "Year": "2013"}, {"Filename": "ETSO_APT_Attacks_Analysis", "Title": "Etso Apt Attacks Analysis  ", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "dc92b79b353eb0e47e71216f3fa1f0c6167e29e7", "Date": "12/20/2013", "Year": "2013"}, {"Filename": "energy-at-risk", "Title": "Energy At Risk: A Study Of It Security In The Energy And Natural Resources Industry", "Source": "KPMG", "Link": "https://app.box.com/s/********************************", "SHA-1": "f03931c7214e71f4bfcc6a5008acb3f4bb1cb0e3", "Date": "12/31/2013", "Year": "2013"}, {"Filename": "targeted_attacks_against_the_energy_sector", "Title": "Targeted Attacks Against The Energy Sector", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "8421ef67d1e6dcc277f07f39f86e21fd89cf1d5a", "Date": "01/13/2014", "Year": "2014"}, {"Filename": "FTA 1001 FINAL 1.15.14", "Title": "New Cdto: A Sneakernet Trojan Solution", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "9767abff87b137695ab8481729ed7130499a0c80", "Date": "01/15/2014", "Year": "2014"}, {"Filename": "h12756-wp-shell-crew", "Title": "Emerging Threat Profile Shell_Crew ", "Source": "RSA", "Link": "https://app.box.com/s/********************************", "SHA-1": "ec6771a81e830f50c2d54b26dc0f6a642439ee09", "Date": "01/21/2014", "Year": "2014"}, {"Filename": "FTA 1011 Follow UP", "Title": "Intruder File Report- Sneakernet Trojan", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "036b1154d4cc2de08dc647eb743c6b4c9d860902", "Date": "01/31/2014", "Year": "2014"}, {"Filename": "unveilingthemask_v1.0", "Title": "Unveiling Careto - The Masked Apt", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "1e4c8aef818d7d0e950974b6c9d2a792969e3a94", "Date": "02/11/2014", "Year": "2014"}, {"Filename": "Operation_SnowMan", "Title": "Operation Snowman: Deputydog Actor Compromises Us Veterans Of Foreign Wars Website", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "114583db8690cd01c60d5758dbe2e2dc1e96fb25", "Date": "02/13/2014", "Year": "2014"}, {"Filename": "XtremeRAT_fireeye", "Title": "Xtremerat: Nuisance Or Threat?", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "186d7913fe80c35a06e76a5c8fd08520f43b4246", "Date": "02/19/2014", "Year": "2014"}, {"Filename": "The_Monju_Incident", "Title": "The Monju Incident ", "Source": "Context", "Link": "https://app.box.com/s/********************************", "SHA-1": "9b66a35bea35d963d1ff07cab2a3914b38e24257", "Date": "02/19/2014", "Year": "2014"}, {"Filename": "Operation_GreedyWonk", "Title": "Operation Greedywonk: Multiple Economic And Foreign Policy Sites Compromised, Serving Up Flash Zero-Day Exploit", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "e162b0015a753a6d85a13296e601c31271868b1f", "Date": "02/20/2014", "Year": "2014"}, {"Filename": "deep-panda-webshells", "Title": "<PERSON>' Shells Mo' Problems - Deep Panda Web Shells", "Source": "Crowdstrike", "Link": "https://app.box.com/s/********************************", "SHA-1": "76a6ea858e3524682ad3ee30251003228db50fb3", "Date": "02/20/2014", "Year": "2014"}, {"Filename": "FTA 1012 STTEAM Final", "Title": "Gathering In The Middle East, Operation Stteam", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "d31648d83d04cc22669f21fa92ee48345e76e062", "Date": "02/23/2014", "Year": "2014"}, {"Filename": "Crowdstrike_The French Connection(Feb-25-14)", "Title": "The French Connection: French Aerospace-Focused CVE-2014-0322 Attack Shares Similarities with 2012 Capstone Turbine Activity", "Source": "Crowdstrike", "Link": "https://app.box.com/s/********************************", "SHA-1": "51e8e24162c94d68637f8252ea6e8f5707b52a23", "Date": "02/25/2014", "Year": "2014"}, {"Filename": "GData_Uroburos_RedPaper_EN_v1", "Title": "Uroburos Highly Complex Espionage Software With Russian Roots", "Source": "Gdata", "Link": "https://app.box.com/s/********************************", "SHA-1": "917691a4f8af50a09926f97bf1be2e0cb71f8c68", "Date": "02/28/2014", "Year": "2014"}, {"Filename": "The_Siesta_Campaign", "Title": "The Siesta Campaign: A New Cybercrime Operation Awakens", "Source": "Trend Micro", "Link": "https://app.box.com/s/********************************", "SHA-1": "1e1f0c599eb1c22360cb9bf8bc30399050e3764b", "Date": "03/06/2014", "Year": "2014"}, {"Filename": "snake_whitepaper", "Title": "Snake Campaign & Cyber Espionage Toolkit", "Source": "BAE Systems", "Link": "https://app.box.com/s/********************************", "SHA-1": "0849ce1f0272c4604d47e464ab56cad0b5b60263", "Date": "03/07/2014", "Year": "2014"}, {"Filename": "Reuters_Turla", "Title": "Suspected Russian Spyware Turla Targets Europe, United States", "Source": "Reuters", "Link": "https://app.box.com/s/********************************", "SHA-1": "94d04c5da4ed33cd78d033ad371aa8472e53d701", "Date": "03/08/2014", "Year": "2014"}, {"Filename": "Op_Clandestine_Fox", "Title": "New Zero-Day Exploit Targeting Internet Explorer Versions 9 Through 11 Identified In Targeted Attacks", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "829bf301061a9a6358c233ca5aa459439fc0aec8", "Date": "04/26/2014", "Year": "2014"}, {"Filename": "fireeye-operation-saffron-rose", "Title": "Operation Saffron Rose", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "41c3f02fe289ddb0d0c5a010f5865b58da939b1c", "Date": "05/13/2014", "Year": "2014"}, {"Filename": "CrowdStrike_Flying_Kitten", "Title": "<PERSON> Scratch Fever: Crowdstrike Tracks Newly Reported Iranian Actor As <PERSON>", "Source": "Crowdstrike", "Link": "https://app.box.com/s/********************************", "SHA-1": "e743ba5074212801ce09ee640730028ab8f41d48", "Date": "05/13/2014", "Year": "2014"}, {"Filename": "Miniduke_twitter", "Title": "Miniduke Still Duking It Out", "Source": "ESET", "Link": "https://app.box.com/s/********************************", "SHA-1": "91c2cdb099060388dd93b0e440a3ff4dff5fd622", "Date": "05/20/2014", "Year": "2014"}, {"Filename": "FTA_1013_RAT_in_a_jar", "Title": "Rat In A Jar: A Phishing Campaign Using Unrecom", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "b1e45b08bfa1db986e1e643291d250a0cda1988c", "Date": "05/21/2014", "Year": "2014"}, {"Filename": "ASERT-Threat-Intelligence-Brief-2014-07-Illuminating-Etumbot-APT", "Title": "Illuminating The Etumbot Apt Backdoor", "Source": "Arbor Networks", "Link": "https://app.box.com/s/********************************", "SHA-1": "51bb16ba51be3c144ac9e09a68732cbe0bb785d5", "Date": "06/06/2014", "Year": "2014"}, {"Filename": "putter-panda", "Title": "Put<PERSON> Panda", "Source": "Crowdstrike", "Link": "https://app.box.com/s/********************************", "SHA-1": "ddadffb91053c4d19590e2035c8eeed14fceca60", "Date": "06/09/2014", "Year": "2014"}, {"Filename": "TrapX_ZOMBIE_Report_Final", "Title": "Anatomy Of The Attack: Zombie Zero", "Source": "Trapx", "Link": "https://app.box.com/s/********************************", "SHA-1": "bd1794d152f04add2aef937826a9cf949c4b25ab", "Date": "06/10/2014", "Year": "2014"}, {"Filename": "Bluecoat_SnakeInTheGrass-Python-Malware-Targeted(06-10-2014)", "Title": "Snake In The Grass: Python-based Malware Used For Targeted Attacks", "Source": "Bluecoat", "Link": "https://app.box.com/s/********************************", "SHA-1": "7132fa8920490fd6f33a464f7018fbd0baffb43b", "Date": "06/10/2014", "Year": "2014"}, {"Filename": "Compromise_Greece_Beijing", "Title": "#9 Blitzanalysis: Embassy Of Greece Beijing - Compromise", "Source": "R136a1", "Link": "https://app.box.com/s/********************************", "SHA-1": "292359e869860f8308c2cf789986fe7c12502553", "Date": "06/20/2014", "Year": "2014"}, {"Filename": "Dragonfly_Threat_Against_Western_Energy_Suppliers", "Title": "Dragonfly: Cyberespionage Attacks Against Energy Suppliers", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "1856b8232153c3cc879662288b34b4a660586a9a", "Date": "06/30/2014", "Year": "2014"}, {"Filename": "circl-tr25-analysis-turla-pfinet-snake-uroburos", "Title": "Tr-25 Analysis - Turla / PNet / Snake/ Uroburos", "Source": "CIRCL", "Link": "https://app.box.com/s/********************************", "SHA-1": "7f3e46c42095721ba79b1a47e26e662eb7492057", "Date": "07/10/2014", "Year": "2014"}, {"Filename": "<PERSON>y_Tiger_Final_Report", "Title": "The Eye Of The Tiger (<PERSON><PERSON>)", "Source": "Airbus", "Link": "https://app.box.com/s/********************************", "SHA-1": "d5a6d2366c4973f06e95bb1201747d0175321952", "Date": "07/11/2014", "Year": "2014"}, {"Filename": "<PERSON><PERSON>_<PERSON>_Kitten_analysis", "Title": "<PERSON><PERSON> (<PERSON> Kitten) Infostealer: Is This The Work Of The Iranian Ajax Security Team?", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "591cef00735f510079e0fe4bd0067ca0150fd004", "Date": "07/20/2014", "Year": "2014"}, {"Filename": "<PERSON><PERSON><PERSON>_Lab_crouching_yeti_appendixes_eng_final", "Title": "Crouching Yet<PERSON>: <PERSON><PERSON>ndi<PERSON>", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "a53ce340535461cc92e274f7c5bfb0d8653d5087", "Date": "07/31/2014", "Year": "2014"}, {"Filename": "EB-YetiJuly2014-Public", "Title": "Energetic Bear _ Crouching Yeti", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "142d4447504e30012d98fea16831f2363c62c5ca", "Date": "07/31/2014", "Year": "2014"}, {"Filename": "KL_report_syrian_malware", "Title": "Syrian Malware, The Ever-Evolving Threat", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "5ceee60079796aa518c5e490feaca4a0d78c031b", "Date": "08/01/2014", "Year": "2014"}, {"Filename": "Gholee_Protective_Edge_themed_spear_phishing_campaign", "Title": "Gholee Protective Edge Themed Spear Phishing Campaign", "Source": "<PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "d78156c4a19f70d219ed76526769d4d5f141a4ed", "Date": "08/04/2014", "Year": "2014"}, {"Filename": "fireeye-sidewinder-targeted-attack", "Title": "Sidewinder Targeted Attack Against Android In The Golden Age Of Ad Libraries", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "0e5301e830d7b19600b2d110115d7c0f0ab86b02", "Date": "08/04/2014", "Year": "2014"}, {"Filename": "ThreatConnect_Operation_Arachnophobia_Report", "Title": "Operation Arachnophobia Caught In The Spider's Web", "Source": "ThreatConnect", "Link": "https://app.box.com/s/********************************", "SHA-1": "2a38d54d1d345d079325d3180c5f0eb8f5d60f8a", "Date": "08/05/2014", "Year": "2014"}, {"Filename": "Operation_Poisoned_Hurricane", "Title": "Operation Poisoned Hurricane", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "73f54d632b5616db3a5977c1514640e19279c056", "Date": "08/06/2014", "Year": "2014"}, {"Filename": "KL_Epic_Turla_Technical_Appendix_20140806", "Title": "The Epic Turla Operation: Solving Some Of The Mysteries Of Snake/Uroboros", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "63e36939c3f90c4ca9d492b03cf04d9f03a4ec2f", "Date": "08/07/2014", "Year": "2014"}, {"Filename": "El_Machete", "Title": "El Machete", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "2bf418f3786cd274b9031a2297313f044054bdfd", "Date": "08/20/2014", "Year": "2014"}, {"Filename": "NetTraveler_Makeover_10th_Birthday", "Title": "Nettraveler Apt Gets A Makeover For 10Th Birthday", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "c264921f168c1cf344cd493d10aeebf92f80fb0c", "Date": "08/27/2014", "Year": "2014"}, {"Filename": "HPSR SecurityBriefing_Episode16_NorthKorea", "Title": "Profiling An Enigma:  The Mystery Of North Korea's Cyber Threat Landscape", "Source": "HP", "Link": "https://app.box.com/s/********************************", "SHA-1": "194656e774aaacb86ae2c48f0c894e82ec68a833", "Date": "08/27/2014", "Year": "2014"}, {"Filename": "Alienvault_Scanbox", "Title": "Scanbox: A Reconnaissance Framework Used With Watering Hole Attacks", "Source": "Alienvault", "Link": "https://app.box.com/s/********************************", "SHA-1": "28c56a1e795cd404308274424d10edcc3e9b4339", "Date": "08/28/2014", "Year": "2014"}, {"Filename": "Syrian_Malware_Team_BlackWorm", "Title": "Connecting The Dots: Syrian Malware Team Uses Blackworm For Attacks", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "543e0df8b308a9383f86d4314d93b5a2e718bd42", "Date": "08/29/2014", "Year": "2014"}, {"Filename": "Darwin_fav_APT_Group", "Title": "Darwin's Favorite Apt Group", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "c24ba893644a149a1f05432c392a716251dca72f", "Date": "09/03/2014", "Year": "2014"}, {"Filename": "XSLCmd_OSX", "Title": "Forced To Adapt: Xslcmd Backdoor Now On Os X", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "b4c64e64f6309c0f424cdf0cdf449576f36cee16", "Date": "09/04/2014", "Year": "2014"}, {"Filename": "Chinese_MITM_Google", "Title": "Analysis Of Chinese Mitm On Google", "Source": "Netresec", "Link": "https://app.box.com/s/********************************", "SHA-1": "850ce69b276f4726de382eabcffa8cb8d68cecfc", "Date": "09/04/2014", "Year": "2014"}, {"Filename": "sec14-paper-ma<PERSON><PERSON>", "Title": "When Governments Hack Opponents: A Look At Actors And Technology", "Source": "Usenix Conference", "Link": "https://app.box.com/s/********************************", "SHA-1": "2cfbb7b89a5e220b21bbf64161dc880c1b644017", "Date": "09/08/2014", "Year": "2014"}, {"Filename": "sec14-paper-hardy", "Title": "Targeted Threat Index: Characterizing And Quantifying Politically-Motivated Targeted Malware", "Source": "Usenix Conference", "Link": "https://app.box.com/s/********************************", "SHA-1": "7aa450d7e2b43175590a1ee2c94f5342152cfc56", "Date": "09/08/2014", "Year": "2014"}, {"Filename": "fireeye-operation-quantum-entanglement", "Title": "Operation Quantum Entanglement", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "bf937bb2315901541521f00abce8d441d272da16", "Date": "09/10/2014", "Year": "2014"}, {"Filename": "cosmicduke_whitepaper", "Title": "Cosmicduke Cosmu With A Twist Of Miniduke", "Source": "F-Secure", "Link": "https://app.box.com/s/********************************", "SHA-1": "f726486a5cc1e42d2030b07d56f87983814226c7", "Date": "09/18/2014", "Year": "2014"}, {"Filename": "th3bug_Watering_Hole_PoisonIvy", "Title": "Recent Watering Hole Attacks Attributed To Apt Group Th3Bug Using Poison Ivy", "Source": "Palo Alto", "Link": "https://app.box.com/s/********************************", "SHA-1": "7fc85f6c70527da8b2c4e6a32e1d4e18c007fcb6", "Date": "09/19/2014", "Year": "2014"}, {"Filename": "blackenergy_whitepaper", "Title": "Blackenergy & Quedagh: The Convergence Of Crimeware  And Apt Attacks", "Source": "F-Secure", "Link": "https://app.box.com/s/********************************", "SHA-1": "efd7b3a3a2bf6e3976411347dc9101fea70c9405", "Date": "09/26/2014", "Year": "2014"}, {"Filename": "Aided_Frame_Aided_Direction", "Title": "Aided <PERSON><PERSON>, Aided Direction (Because It's A Redirect)", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "83adcb352168b2d345155cf4ec7bbc876bb89849", "Date": "09/26/2014", "Year": "2014"}, {"Filename": "PAN_Nitro", "Title": "New Indicators Of Compromise For Apt Group Nitro Uncovered", "Source": "Palo Alto", "Link": "https://app.box.com/s/********************************", "SHA-1": "bc4cef4cabbcf83dbc1c72f736acb6207b260216", "Date": "10/03/2014", "Year": "2014"}, {"Filename": "Democracy_HongKong_Under_Attack", "Title": "Democracy In Hong Kong Under Attack", "Source": "Volexity", "Link": "https://app.box.com/s/********************************", "SHA-1": "9439561abc998b7f0f733142bc44f717b2615374", "Date": "10/09/2014", "Year": "2014"}, {"Filename": "ZoxPNG_Full_Analysis-Final", "Title": "Zoxpng Analysis", "Source": "Novetta", "Link": "https://app.box.com/s/********************************", "SHA-1": "d24ff42b2d02ba406d46f3912f1f7bd2d38b6113", "Date": "10/14/2014", "Year": "2014"}, {"Filename": "Sandworm_briefing2", "Title": "Russian Cyber Espionage Campaign - Sandworm Team", "Source": "iSight Partners", "Link": "https://app.box.com/s/********************************", "SHA-1": "cccc6053fa78cef9f8a28efdaa07c8cfa6a73cc2", "Date": "10/14/2014", "Year": "2014"}, {"Filename": "Hikit_Analysis-Final", "Title": "Hikit Analysis", "Source": "Novetta", "Link": "https://app.box.com/s/********************************", "SHA-1": "5c4415913a16a0331600816bb4cf8a1954e743dd", "Date": "10/14/2014", "Year": "2014"}, {"Filename": "Group_72", "Title": "Threat Spotlight: Group 72", "Source": "Cisco", "Link": "https://app.box.com/s/********************************", "SHA-1": "20dd2aaae24812d78cd1c5e32c68b7998e00e0ca", "Date": "10/14/2014", "Year": "2014"}, {"Filename": "OrcaRAT", "Title": "Orcarat - A Whale Of A Tale", "Source": "PWC", "Link": "https://app.box.com/s/********************************", "SHA-1": "13a055fe7be7e55dcce0035eaf1990fbe8406c98", "Date": "10/20/2014", "Year": "2014"}, {"Filename": "tactical-intelligence-bulletin---sofacy-phishing-", "Title": "Tactical Intelligence Bulletin Sofacy <PERSON>shing", "Source": "PWC", "Link": "https://app.box.com/s/********************************", "SHA-1": "643c7e975121b4614156fc4f29de09b4fd1f0026", "Date": "10/22/2014", "Year": "2014"}, {"Filename": "wp-operation-pawn-storm", "Title": "Operation Pawn Storm Using Decoys To Evade Detection", "Source": "Trend Micro", "Link": "https://app.box.com/s/********************************", "SHA-1": "4ff4b93665664603623bc7001e3ca961b8b78b9f", "Date": "10/23/2014", "Year": "2014"}, {"Filename": "Modified_Binaries_Tor", "Title": "Modified Binaries Tor", "Source": "<PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "c0708a3efcf32d02cfb5348f87fe140ae6f470e8", "Date": "10/23/2014", "Year": "2014"}, {"Filename": "LeoUncia_OrcaRat", "Title": "Leouncia And Orcarat", "Source": "Airbus", "Link": "https://app.box.com/s/********************************", "SHA-1": "4bd6fa0c0a85f9041cecd54d722decdb4e817fe0", "Date": "10/24/2014", "Year": "2014"}, {"Filename": "Novetta_Operation-SMN(10-24-2014)", "Title": "Operation SMN", "Source": "Novetta", "Link": "https://app.box.com/s/********************************", "SHA-1": "6b69bd2d1f6def7c593fa7feeb652bf938ee5632", "Date": "10/24/2014", "Year": "2014"}, {"Filename": "pwc_ScanBox_framework", "Title": "Scanbox Framework: Who's Affected, And Who's Using It?", "Source": "PWC", "Link": "https://app.box.com/s/********************************", "SHA-1": "c264d97adeb81f59b0642de9a782f6fe545ed062", "Date": "10/27/2014", "Year": "2014"}, {"Filename": "Micro-Targeted-Malvertising-WP-10-27-14-1", "Title": "Micro-Targeted Malvertising Via Real-Time Ad Bidding", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "20fa586304cbbfaf23453c1bbe7033de398bd9fb", "Date": "10/27/2014", "Year": "2014"}, {"Filename": "ICS_Havex_backdoors", "Title": "Full Disclosure Of Havex Trojans", "Source": "Netresec", "Link": "https://app.box.com/s/********************************", "SHA-1": "50b165c213697facb2410591c3ddf772b95fc805", "Date": "10/27/2014", "Year": "2014"}, {"Filename": "Group72_Opening_ZxShell", "Title": "Threat Spotlight: Group 72, Opening The Zxshell", "Source": "Cisco", "Link": "https://app.box.com/s/********************************", "SHA-1": "116309e7121bc8b0e66e4166c06f7b818e1d3629", "Date": "10/28/2014", "Year": "2014"}, {"Filename": "apt28", "Title": "Apt28: A Window Into Russia's Cyber Espionage Operations", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "1d9b39654f8c7678b3e2c30e378b2e14021b5d44", "Date": "10/28/2014", "Year": "2014"}, {"Filename": "sophos-rotten-tomato-campaign", "Title": "The Rotten Tomato Campaign", "Source": "<PERSON>ph<PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "23f0de5e628eccdfc670412485653f3662ab5594", "Date": "10/30/2014", "Year": "2014"}, {"Filename": "GDATA_TooHash_CaseStudy_102014_EN_v1", "Title": "Operation Toohash How Targeted Attacks Work ", "Source": "Gdata", "Link": "https://app.box.com/s/********************************", "SHA-1": "85fcdce7427c13906658f1835acaef7103c22ad3", "Date": "10/31/2014", "Year": "2014"}, {"Filename": "Operation_Poisoned_Handover", "Title": "Operation Poisoned Handover: Unveiling Ties Between Apt Activity In Hong Kong's Pro-Democracy Movement", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "40674e70c595d30f19b2c2636ed7d9dc6b146e8e", "Date": "11/03/2014", "Year": "2014"}, {"Filename": "BlackEnergy2_Plugins_Router", "Title": "Be2 Custom Plugins, Router Abuse, And Target Profiles", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "15daf73d022246964c2938a889361aba40e7d08b", "Date": "11/03/2014", "Year": "2014"}, {"Filename": "darkhotelappendixindicators_kl", "Title": "Darkhotel Indicators Of Compromise", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "abddcfeac71a991c480810c82d7e972c74251329", "Date": "11/10/2014", "Year": "2014"}, {"Filename": "darkhotel_kl_07.11", "Title": "The Darkhotel Apt A Story Of Unusual Hospitality v1.0", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "dfd64e9a601283c76ae3f28875166695dc354a21", "Date": "11/10/2014", "Year": "2014"}, {"Filename": "<PERSON><PERSON><PERSON>_Darkhotel_kl_07.11_1.1(11-10-2014)", "Title": "The Darkhotel APT A Story of Unusual Hospitality v1.1", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "fab8b6fddc50a533d5b537b27156cb265fea7f02", "Date": "11/10/2014", "Year": "2014"}, {"Filename": "The_Uroburos_case", "Title": "The Uroburos Case: New Sophisticated Rat Identified", "Source": "Gdata", "Link": "https://app.box.com/s/********************************", "SHA-1": "96173322b936132aa4cdc0328e4a247d40ae5152", "Date": "11/11/2014", "Year": "2014"}, {"Filename": "Korplug_Afghanistan_Tajikistan", "Title": "Korplug Military Targeted Attacks: Afghanistan & Tajikistan", "Source": "ESET", "Link": "https://app.box.com/s/********************************", "SHA-1": "b336dd349c5b4620f04d98b90282c7ae900a3a42", "Date": "11/12/2014", "Year": "2014"}, {"Filename": "Operation_CloudyOmega_Ichitaro", "Title": "Operation Cloudyomega: Ichitaro Zero-Day And Ongoing Cyberespionage Campaign Targeting Japan", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "a4520f8f85d13aa469ff3e1b6d333e4c9a290e7a", "Date": "11/13/2014", "Year": "2014"}, {"Filename": "roaming_tiger_zeronights_2014", "Title": "Roaming Tiger", "Source": "ESET", "Link": "https://app.box.com/s/********************************", "SHA-1": "9d116269da44e22cc6f1496570bb4d56f7cc277b", "Date": "11/14/2014", "Year": "2014"}, {"Filename": "OnionDuke_Tor", "Title": "Onionduke: Apt Attacks Via The Tor Network - F-Secure Weblog : News From The Lab", "Source": "F-Secure", "Link": "https://app.box.com/s/********************************", "SHA-1": "c50a95070633ecc76898b9f16ded848414747156", "Date": "11/14/2014", "Year": "2014"}, {"Filename": "Der<PERSON>bi_Server_Analysis-Final", "Title": "<PERSON><PERSON><PERSON> (Server Variant) Analysis", "Source": "Novetta", "Link": "https://app.box.com/s/********************************", "SHA-1": "d246e3075bbd31f04b2a6efb53ad7d9e9faa0e96", "Date": "11/14/2014", "Year": "2014"}, {"Filename": "EvilB<PERSON>ny_Suspect4_v1.0", "Title": "Evil Bunny: Suspect #4", "Source": "<PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "814556f1a03e93364f4dc54555cb27549288e061", "Date": "11/20/2014", "Year": "2014"}, {"Filename": "OperationDoubleTap", "Title": "Operation Double Tap", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "e8fcb14805853185b36093119094085f0f7f86a2", "Date": "11/21/2014", "Year": "2014"}, {"Filename": "regin-analysis", "Title": "Regin: Top-Tier Espionage Tool Enables Stealthy Surveillance", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "40f76477ba1f453b341743b53113ebd705c1bb75", "Date": "11/23/2014", "Year": "2014"}, {"Filename": "Regis_The_Intercept", "Title": "Secret Malware In European Union Attack Linked To U.S. And British Intelligence", "Source": "The Intercept", "Link": "https://app.box.com/s/********************************", "SHA-1": "50be553c398b512ddd9741a0ab0350dccd600a3b", "Date": "11/24/2014", "Year": "2014"}, {"Filename": "Kaspersky_Lab_whitepaper_Regin_platform_eng", "Title": "The Regin Platform Nation-State Ownership Of Gsm Networks", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "5bbf6a633076473dc4b2afb6d166c8caa84463e4", "Date": "11/24/2014", "Year": "2014"}, {"Filename": "DEEP_PANDA_Sakula", "Title": "I Am Ironman: Deep Panda Uses Sakula Malware To Target Organizations In Multiple Sectors", "Source": "Crowdstrike", "Link": "https://app.box.com/s/********************************", "SHA-1": "02be2ef6587e940656cde835354d0073c4dce232", "Date": "11/24/2014", "Year": "2014"}, {"Filename": "rpt-fin4", "Title": "Hacking The Street?  Fin4 Likely Playing The Market", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "e5e09d247f5d25d7c960a3ef3231cef9d59a2b61", "Date": "12/01/2014", "Year": "2014"}, {"Filename": "OperationCleaver_The_Notepad_Files", "Title": "Operation Cleaver: The Notepad Files", "Source": "Cylance", "Link": "https://app.box.com/s/********************************", "SHA-1": "114d677bac083a956038f44abec2bf2a59e1e080", "Date": "12/03/2014", "Year": "2014"}, {"Filename": "<PERSON><PERSON><PERSON>_2_<PERSON><PERSON>", "Title": "The 'Penquin' Turla", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "dc20be49cbcecaf38bde2105a54580eb96605c25", "Date": "12/08/2014", "Year": "2014"}, {"Filename": "bcs_wp_InceptionReport_EN_v12914", "Title": "The Inception Framework: Cloud-Hosted Apt", "Source": "Bluecoat", "Link": "https://app.box.com/s/********************************", "SHA-1": "975c44773b456562f9ab5f9986c2102a21b618bd", "Date": "12/09/2014", "Year": "2014"}, {"Filename": "w64_regin_stage_1", "Title": "W64/Regin, Stage #1", "Source": "F-Secure", "Link": "https://app.box.com/s/********************************", "SHA-1": "c5355707644b6948069345e2e8bac429e39f882d", "Date": "12/10/2014", "Year": "2014"}, {"Filename": "w32_regin_stage_1", "Title": "W32/Regin, Stage #1", "Source": "F-Secure", "Link": "https://app.box.com/s/********************************", "SHA-1": "62d5fdb316ad5b0c5e3afb5919785df4c557f25b", "Date": "12/10/2014", "Year": "2014"}, {"Filename": "korea_power_plant_wiper", "Title": "Vulnerability, Malicious Code Appeared In The Mbr Destruction Function Using Hangul File", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "fefd7ff6b2b254bd2e05784b51758c5d90acc06f", "Date": "12/10/2014", "Year": "2014"}, {"Filename": "CloudAtlas_RedOctober_APT", "Title": "Cloud Atlas: Redoctober Apt Is Back In Style", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "a9970d82d1b539ee63838619fcb9aaaae5f0c51d", "Date": "12/10/2014", "Year": "2014"}, {"Filename": "Vinself_steganography", "Title": "<PERSON><PERSON> Now With Steganography", "Source": "Airbus", "Link": "https://app.box.com/s/********************************", "SHA-1": "ae90917c7abe0c38ae289be9589f04c6fa7184c0", "Date": "12/12/2014", "Year": "2014"}, {"Filename": "FTA_1014_Bots_Machines_and_the_Matrix", "Title": "<PERSON><PERSON>, Machines, And The Matrix", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "92f526d9a5e14080fdbec90ba2385617bbf19a10", "Date": "12/12/2014", "Year": "2014"}, {"Filename": "Wiper_Malware", "Title": "Wiper Malware _ A Detection Deep Dive", "Source": "Cisco", "Link": "https://app.box.com/s/********************************", "SHA-1": "f7db20ae4b3f4784a3b4ac346424872858370a18", "Date": "12/17/2014", "Year": "2014"}, {"Filename": "Targeting_Syrian_ISIS_Critics", "Title": "Malware Attack Targeting Syrian Isis Critics", "Source": "Citizen Lab, Cyber Arabs", "Link": "https://app.box.com/s/********************************", "SHA-1": "d0f24df94063d28e13c08fd2aeb9522034da3dea", "Date": "12/18/2014", "Year": "2014"}, {"Filename": "TA14-353A_wiper", "Title": "<PERSON><PERSON> (Ta14-353A) Targeted Destructive Malware", "Source": "US-CERT", "Link": "https://app.box.com/s/********************************", "SHA-1": "0e575c64a7603a1709b1ae37e286f420128d2096", "Date": "12/19/2014", "Year": "2014"}, {"Filename": "operation-poisoned-helmand", "Title": "Operation Poisoned <PERSON><PERSON><PERSON>", "Source": "ThreatConnect", "Link": "https://app.box.com/s/********************************", "SHA-1": "1a4508469960b248ba713cecf34653c59fd460f1", "Date": "12/21/2014", "Year": "2014"}, {"Filename": "Anunak_APT_against_financial_institutions", "Title": "Anunak: Apt Against Financial Institutions", "Source": "Group-IB, FOX-IT", "Link": "https://app.box.com/s/********************************", "SHA-1": "c1b7c2bec86e8edf8bba650c6fa506319198e3c3", "Date": "12/22/2014", "Year": "2014"}, {"Filename": "Skeleton_Key_Analysis", "Title": "Skeleton Key Malware Analysis", "Source": "Dell Secureworks", "Link": "https://app.box.com/s/********************************", "SHA-1": "28f35f4b95e66030cf2a330bae394bbf8805b34f", "Date": "01/12/2015", "Year": "2015"}, {"Filename": "DTL-12012015-01", "Title": "Insight In To A Strategic Web Compromise And Attack Campaign Against Hong Kong Infrastructure", "Source": "Dragon Threat Labs", "Link": "https://app.box.com/s/********************************", "SHA-1": "2aa6e47d8b9549b8ca2ea62db6384bb4db682bcf", "Date": "01/12/2015", "Year": "2015"}, {"Filename": "Agent.BTZ_to_ComRAT", "Title": "Evolution Of Sophisticated Spyware: From Agent.Btz To Comrat", "Source": "Gdata", "Link": "https://app.box.com/s/********************************", "SHA-1": "cde02057689886c29438815cbeed8ebe860a0ab2", "Date": "01/15/2015", "Year": "2015"}, {"Filename": "Project_Cobra_Analysis", "Title": "Analysis Of Project Cobra", "Source": "Gdata", "Link": "https://app.box.com/s/********************************", "SHA-1": "6df16b7ff93a44fcbec3b656645631b864175bcf", "Date": "01/20/2015", "Year": "2015"}, {"Filename": "Inception_APT_Analysis_Bluecoat", "Title": "Reversing The Inception APT Malware", "Source": "Bluecoat", "Link": "https://app.box.com/s/********************************", "SHA-1": "486a65ba17141147d3d9fff2a0c26109edf78fab", "Date": "01/20/2015", "Year": "2015"}, {"Filename": "waterbug-attack-group", "Title": "The Waterbug Attack Group", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "d6b2e4dba3b801252a62e0dade5c8ab71d2eefb1", "Date": "01/22/2015", "Year": "2015"}, {"Filename": "Scarab_Russian", "Title": "Scarab Attackers Took Aim At Select Russian Targets Since 2012", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "247676579b83264faf32e171f9187bbdbb057c7f", "Date": "01/22/2015", "Year": "2015"}, {"Filename": "Regin_<PERSON><PERSON><PERSON>_Legspin", "Title": "An Analysis Of Regin's Hopscotch And Legspin", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "630eea3f1bc9158570c53d70fc70b31003305f5c", "Date": "01/22/2015", "Year": "2015"}, {"Filename": "P2P_PlugX_Analysis", "Title": "Analysis Of A Recent Plugx Variant - P2P Plugx", "Source": "JPCERT", "Link": "https://app.box.com/s/********************************", "SHA-1": "d2e17e228e02df878f807b112f78afdc13cc6bca", "Date": "01/29/2015", "Year": "2015"}, {"Filename": "Backdoor.Winnti_<PERSON>.<PERSON>", "Title": "Backdoor.Winnti Attackers Have A Skeleton In Their Closet?", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "c9a33400ab60741ff0c77a9829f3e04cfe39f2d2", "Date": "01/29/2015", "Year": "2015"}, {"Filename": "rpt-behind-the-syria-conflict", "Title": "Behind The Syrian Conflict's Digital Front Lines", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "4751fe686fb4e305ef49296f46ac33139114232b", "Date": "02/02/2015", "Year": "2015"}, {"Filename": "PawnStorm_iOS", "Title": "Pawn Storm Update: Ios Espionage App Found", "Source": "Trend Micro", "Link": "https://app.box.com/s/********************************", "SHA-1": "4e645016b3627272cceb28a3b8bbba536eb9a6b4", "Date": "02/04/2015", "Year": "2015"}, {"Filename": "GlobalThreatIntelReport", "Title": "Global Threat Intel Report", "Source": "Crowdstrike", "Link": "https://app.box.com/s/********************************", "SHA-1": "86f4a308b99a2a9cb335dc06457dd09399c05a29", "Date": "02/10/2015", "Year": "2015"}, {"Filename": "operation-arid-viper-whitepaper-en", "Title": "Operation Arid Viper: Bypassing The Iron Dome", "Source": "Trend Micro", "Link": "https://app.box.com/s/********************************", "SHA-1": "9134d57a818f98608a53b53dcfb520716d9eb1c3", "Date": "02/16/2015", "Year": "2015"}, {"Filename": "Equation_group_questions_and_answers", "Title": "Equation Group: Questions And Answers", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "41aa5bd1ed03d80650a89de3649d051f900b958b", "Date": "02/16/2015", "Year": "2015"}, {"Filename": "Carbanak_APT_eng", "Title": "Carbanak APT The Great Bank Robbery", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "99af231600ee8fd83b4b4fb057429e60cd9d21c4", "Date": "02/16/2015", "Year": "2015"}, {"Filename": "The-Desert-Falcons-targeted-attacks", "Title": "The Desert Falcons Targeted Attacks", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "0f4d8ba248dde47b514161014a57885aed084ce2", "Date": "02/17/2015", "Year": "2015"}, {"Filename": "Elephantosis", "Title": "Shooting Elephants", "Source": "Netzpolitik", "Link": "https://app.box.com/s/********************************", "SHA-1": "c8f384ae75119626032d4b42f34e883269dbf2a4", "Date": "02/18/2015", "Year": "2015"}, {"Filename": "cto-tib-********-01a", "Title": "Scanbox Ii", "Source": "PWC", "Link": "https://app.box.com/s/********************************", "SHA-1": "c73cbb8fa22e16920e3cbf51226f3fd8010d38bb", "Date": "02/24/2015", "Year": "2015"}, {"Filename": "rpt-southeast-asia-threat-landscape", "Title": "Southeast Asia: An Evolving Cyber Threat Landscape ", "Source": "FireEye, Singtel", "Link": "https://app.box.com/s/********************************", "SHA-1": "7c69db91f21ee20f7abcb4d95f21c4a17bfa6d17", "Date": "02/25/2015", "Year": "2015"}, {"Filename": "plugx-goes-to-the-registry-and-india", "Title": "Plugx Goes To The Registry (And India)", "Source": "<PERSON>ph<PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "fc1d350810c19c94b1f3642fa08f50bf914ab821", "Date": "02/25/2015", "Year": "2015"}, {"Filename": "Anthem_hack_all_roads_lead_to_China", "Title": "The Anthem Hack: All Roads Lead To China", "Source": "ThreatConnect", "Link": "https://app.box.com/s/********************************", "SHA-1": "40559d68f1a3d25639408209de18d8ee395ae08a", "Date": "02/27/2015", "Year": "2015"}, {"Filename": "Tibetan-Uprising-Day-Malware-Attacks_websitepdf", "Title": "Tibetan Uprising Day Malware Attacks", "Source": "Citizen Lab", "Link": "https://app.box.com/s/********************************", "SHA-1": "d17fdf8935e094b2a34cde539abc85eec3533941", "Date": "03/10/2015", "Year": "2015"}, {"Filename": "Inside_EquationDrug_Espionage_Platform", "Title": "Inside The Equationdrug Espionage Platform", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "a9a89dbd99ed439abcfced111affc69f9728fc7a", "Date": "03/11/2015", "Year": "2015"}, {"Filename": "wp-operation-woolen-goldfish", "Title": "Operation Woolen-Goldfish When <PERSON><PERSON> Go <PERSON>", "Source": "Trend Micro", "Link": "https://app.box.com/s/********************************", "SHA-1": "73a8169eff8f50cefe587c1097d67fb45e71a046", "Date": "03/19/2015", "Year": "2015"}, {"Filename": "volatile-cedar-technical-report", "Title": "Volatile Cedar Threat Intelligence And Research", "Source": "Checkpoint", "Link": "https://app.box.com/s/********************************", "SHA-1": "7cd5b12fa38705e254296133991410754f1678ab", "Date": "03/31/2015", "Year": "2015"}, {"Filename": "Novetta_winntianalysis(04-07-2015)", "Title": "WINNTI Analysis", "Source": "Novetta", "Link": "https://app.box.com/s/********************************", "SHA-1": "70ee75103d9fc301b7638712ba13c86014b8a0ad", "Date": "04/07/2015", "Year": "2015"}, {"Filename": "RSA-IR-Case-Study(Apr-8-15)", "Title": "RSA Incident Response: An APT Case Study", "Source": "RSA", "Link": "https://app.box.com/s/********************************", "SHA-1": "f2755e9e4e380e2a8357916862e145965c6e7365", "Date": "04/08/2015", "Year": "2015"}, {"Filename": "rpt-apt30", "Title": "APT30 And The Mechanics Of A Long-Running Cyber Espionage Operation", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "1b83984e2c9515c59885bc0078e3b1bed5d513b2", "Date": "04/12/2015", "Year": "2015"}, {"Filename": "The Chronicles of the Hellsing APT_ the Empire Strikes Back - Securelist", "Title": "The Chronicles Of The Hellsing APT: The Empire Strikes Back", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "3991aeb7aa51f81e0742f06b833b055aae662bf9", "Date": "04/15/2015", "Year": "2015"}, {"Filename": "Indicators_of_Compormise_Hellsing", "Title": "Hellsing Indicators Of Compromise", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "5b22b494cfe329c352948530cb19b6dc5067ca44", "Date": "04/15/2015", "Year": "2015"}, {"Filename": "Operation RussianDoll", "Title": "Operation Russiandoll: Adobe & Windows ZeroDay Exploits Likely leveraged By Russia's APT28", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "3bd10169fc704a20a702c3fc61633d68843f195c", "Date": "04/18/2015", "Year": "2015"}, {"Filename": "cto-tib-20150420-01a", "Title": "Sofacy II_ Same Sofacy, Different Day", "Source": "PWC", "Link": "https://app.box.com/s/********************************", "SHA-1": "417a791693f2554ee1ec94564467168ea58e2cfb", "Date": "04/20/2015", "Year": "2015"}, {"Filename": "The CozyDuke APT - Securelist", "Title": "The Cozyduke APT", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "46f4a7b1ec66e3aa1f6a93d64d9a4d3363621636", "Date": "04/21/2015", "Year": "2015"}, {"Filename": "<PERSON><PERSON><PERSON><PERSON>", "Title": "<PERSON>zy<PERSON><PERSON>", "Source": "F-Secure", "Link": "https://app.box.com/s/********************************", "SHA-1": "7f0cdde2b33261a9a35446bb2cb51c310539fa50", "Date": "04/22/2015", "Year": "2015"}, {"Filename": "OperationClandestineWolf", "Title": "Operation Clandestine Wolf _ Adobe Flash Zero-Day In APT3 Phishing Campaign", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "da36e8623013f441bc3dd841e695ceb1f03db496", "Date": "04/26/2015", "Year": "2015"}, {"Filename": "Attacks against Israeli & Palestinian interests - Cyber security updates", "Title": "Attacks Against Israeli & Palestinian Interests", "Source": "PWC", "Link": "https://app.box.com/s/********************************", "SHA-1": "2babeca1ce5aff70f24684cd80ed45ef43ec9a17", "Date": "04/27/2015", "Year": "2015"}, {"Filename": "Ahnlab_Targeted-Attack-on-Frances-TV5Monde(May-5-15)", "Title": "Targeted Attack on France's TV5Monde", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "b6aec94a79b7af64566cf734ef2a703c3d670e1c", "Date": "05/05/2015", "Year": "2015"}, {"Filename": "Dissecting-the-Kraken", "Title": "Dissecting The Kraken", "Source": "Gdata", "Link": "https://app.box.com/s/********************************", "SHA-1": "c478f5c474609cc5401648c0a55213f3a7137344", "Date": "05/07/2015", "Year": "2015"}, {"Filename": "FSOFACY", "Title": "APT28 Targets  Financial Markets: Zero Day Hashes Released", "Source": "root9b", "Link": "https://app.box.com/s/********************************", "SHA-1": "3bda90269f9a49360befe7f9a00f832c57af89c2", "Date": "05/10/2015", "Year": "2015"}, {"Filename": "Cylance SPEAR Team_ A Threat Actor Resurfaces", "Title": "Cylance Spear Team: A Threat Actor Resurfaces", "Source": "Cylance", "Link": "https://app.box.com/s/********************************", "SHA-1": "2c19d922bfa84a0205d9142124caaa51dc2021f5", "Date": "05/13/2015", "Year": "2015"}, {"Filename": "wp-operation-tropic-trooper", "Title": "Operation Tropic Trooper: Relying On Tried-And-Tested Flaws To Infiltrate Secret Keepers", "Source": "Trend Micro", "Link": "https://app.box.com/s/********************************", "SHA-1": "93b3ec0cec9636d7815424be3030ae54c2eb79b5", "Date": "05/14/2015", "Year": "2015"}, {"Filename": "CmstarDownloader_<PERSON>rid_Enfal_Cousin", "Title": "Cmstar Downloader: <PERSON><PERSON> And Enfal's New Cousin", "Source": "Palo Alto", "Link": "https://app.box.com/s/********************************", "SHA-1": "155c112f73a973ecf710fab5caa6434212275d81", "Date": "05/18/2015", "Year": "2015"}, {"Filename": "oil-tanker-en", "Title": "Operation Oil Tanker: The Phantom Menace", "Source": "Pandalabs", "Link": "https://app.box.com/s/********************************", "SHA-1": "538701feeead706e34d24cf2b831071ac2f600cb", "Date": "05/19/2015", "Year": "2015"}, {"Filename": "TheNaikonAPT-MsnMM2", "Title": "The Msnmm Campaigns: The Earliest Naikon APT Campaigns", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "b91ed59b5aea02b712f1ea974fb4cf171ea8ba44", "Date": "05/21/2015", "Year": "2015"}, {"Filename": "Dissecting-LinuxMoose", "Title": "Dissecting Linux/Moose: The Analysis Of A Linux Router-Based Worm Hungry For Social Networks", "Source": "ESET", "Link": "https://app.box.com/s/********************************", "SHA-1": "198195bf91a009bdf666d630230d86b7b1d60bb9", "Date": "05/26/2015", "Year": "2015"}, {"Filename": "ANALYSIS-ON-APT-TO-BE-ATTACK-THAT-FOCUSING-ON-CHINAS-GOVERNMENT-AGENCY-", "Title": "Analysis On APT-To-Be Attack That Focusing On China's Government Agency", "Source": "Antiy CERT", "Link": "https://app.box.com/s/********************************", "SHA-1": "42917d2bb4535fc6369cdd68bf82b7e7d28ebadf", "Date": "05/27/2015", "Year": "2015"}, {"Filename": "Grabit", "Title": "Grabit And The Rats", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "4855ba957702d0393cb7ade531b46625426d9192", "Date": "05/28/2015", "Year": "2015"}, {"Filename": "OceanLotusReport", "Title": "<PERSON><PERSON><PERSON>", "Source": "SkyEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "98e849a6be7fb956f5b17a183a2e00048f3bab25", "Date": "05/29/2015", "Year": "2015"}, {"Filename": "Thamar-Reservoir", "Title": "An Iranian Cyber-Attack Campaign Against Targets In The Middle East", "Source": "<PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "a0c391fec9b1faa80a0c713bd6aa37a7129adda7", "Date": "06/03/2015", "Year": "2015"}, {"Filename": "BlueTermite_Japan", "Title": "Blue Termite (Internet Watch)", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "fee0f49a312915de5c41ebdd1eaf8aefacde1eaf", "Date": "06/04/2015", "Year": "2015"}, {"Filename": "duqu2_crysys", "Title": "Duqu 2.0: A Comparison To Duqu ", "Source": "CrySyS Lab", "Link": "https://app.box.com/s/********************************", "SHA-1": "6c4fcf640dfbedbdddb724e69d0ed84319b0cf6e", "Date": "06/10/2015", "Year": "2015"}, {"Filename": "Symantec_Duqu2-Reemergence-aggressive-cyberespionage-threat(06-10-2015)", "Title": "Duqu 2.0: Reemergence of an aggressive cyberespionage threat", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "efdcd716cde10b6f13d76b51e2f0c5a13b078047", "Date": "06/10/2015", "Year": "2015"}, {"Filename": "The_Mystery_of_Duqu_2_0_a_sophisticated_cyberespionage_actor_returns", "Title": "The Duqu 2.0 Technical Details", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "fe05831d3dc661e418f969045f0087ba642fb27b", "Date": "06/11/2015", "Year": "2015"}, {"Filename": "The Naikon APT - Securelist", "Title": "The Naikon APT: Tracking Down Geo-Political Intelligence Across APAC, One Nation At A Time", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "af489e7e52483efe28d8f20f15be5d1dbae62a55", "Date": "06/15/2015", "Year": "2015"}, {"Filename": "Targeted-Attacks-against-Tibetan-and-Hong-Kong-Groups-Exploiting-CVE-2014-4114", "Title": "Target Attacks Against Tibetan And Hong Kong Groups Exploiting CVE-2014-4114 ", "Source": "Citizen Lab", "Link": "https://app.box.com/s/********************************", "SHA-1": "154083bd059ac6bb001e247f7e03d6189fa93362", "Date": "06/15/2015", "Year": "2015"}, {"Filename": "unit42-operation-lotus-blossom", "Title": "Operation Lotusblossom", "Source": "Palo Alto", "Link": "https://app.box.com/s/********************************", "SHA-1": "3bcbddd61cc7df02fad5bdc692e956bac590fe98", "Date": "06/16/2015", "Year": "2015"}, {"Filename": "winnti_pharmaceutical", "Title": "Games Are Over: Winnti Is Now Targeting Pharmaceutical Companies", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "8b870691f84547afc34c08c494f92a21f6d1dc3e", "Date": "06/22/2015", "Year": "2015"}, {"Filename": "UnFIN4ished_Business_pwd", "Title": "Unfin4Ished Business", "Source": "PWC", "Link": "https://app.box.com/s/********************************", "SHA-1": "5414153144f453be553af09c69bb1300c7678f79", "Date": "06/24/2015", "Year": "2015"}, {"Filename": "ESET_<PERSON>_French", "Title": "Dino: The Latest Spying Malware From An Allegedly French Espionage Group Analyzed", "Source": "ESET", "Link": "https://app.box.com/s/********************************", "SHA-1": "fdf388b793a73c47a7caab35a5c4645c83c0931a", "Date": "06/30/2015", "Year": "2015"}, {"Filename": "WildNeutron_Economic_espionage", "Title": "Wild Neutron _ Economic Espionage Threat Actor Returns With New Tricks", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "a73fcc0ce6923f3a6ce537ec8214cb7b539fe343", "Date": "07/08/2015", "Year": "2015"}, {"Filename": "butterfly-corporate-spies-out-for-financial-gain", "Title": "Butterfly: Corporate Spies Out For Financial Gain", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "bf41e63f1493152c0d82f2b800099fc4170ea9f1", "Date": "07/09/2015", "Year": "2015"}, {"Filename": "Forkmeiamfamous_SeaDuke", "Title": "\"Forkmeiamfamous\": <PERSON><PERSON><PERSON>, Latest Weapon In The Duke Armory", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "006d625ab23c5f9d849608506c77d45afec4c720", "Date": "07/13/2015", "Year": "2015"}, {"Filename": "MiniDionis_CozyCar_Seaduke", "Title": "Tracking Minidionis: Cozycar's New Ride Is Related To Seaduke", "Source": "Palo Alto", "Link": "https://app.box.com/s/********************************", "SHA-1": "d2e6c0c6d2e8dc72677482b8b4271568a3b2a9b9", "Date": "07/14/2015", "Year": "2015"}, {"Filename": "WateringHole_Aerospace_CVE-2015-5122_IsSpace", "Title": "Watering Hole Attack On Aerospace Firm Exploits CVE-2015-5122 To Install Isspace Backdoor", "Source": "Palo Alto", "Link": "https://app.box.com/s/********************************", "SHA-1": "13feea5bb8a1f837e3772daf151e343086061f6a", "Date": "07/20/2015", "Year": "2015"}, {"Filename": "China_Peace_Palace", "Title": "China Hacks The Peace Palace: All Your Eez's Are Belong To Us", "Source": "ThreatConnect", "Link": "https://app.box.com/s/********************************", "SHA-1": "4aa116bc762a0e2ac8ad635799c2a1acc49de6c4", "Date": "07/20/2015", "Year": "2015"}, {"Filename": "Duke_cloud_Linux", "Title": "Duke APT Group's Latest Tools: Cloud Services And Linux Support", "Source": "F-Secure", "Link": "https://app.box.com/s/********************************", "SHA-1": "8ee4f88f4734adc592190027018a461471e8204a", "Date": "07/22/2015", "Year": "2015"}, {"Filename": "apt29-hammertoss-stealthy-tactics-define-a", "Title": "Hammertoss: Stealthy Tactics Define A Russian Cyber Threat Group", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "05763e7c36f1120b89cedba2c39ab4680b8ba28f", "Date": "07/27/2015", "Year": "2015"}, {"Filename": "the-black-vine-cyberespionage-group", "Title": "The Black Vine Cyberespionage Group", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "758520009778853bd186c8527b1cd73ee373ca36", "Date": "07/28/2015", "Year": "2015"}, {"Filename": "Operation-Potao-Express_final_v2", "Title": "Operation Potao Express: Analysis Of A Cyber-Espionage Toolkit", "Source": "ESET", "Link": "https://app.box.com/s/********************************", "SHA-1": "18e19f0aa8caf36fbb424ab650f87bb192d3434a", "Date": "07/31/2015", "Year": "2015"}, {"Filename": "NATO_CCDCOE_CyberWarinPerspective_full_book(08-03-2015)", "Title": "Cyber war in perspective: Russian aggression against Ukraine", "Source": "NATO", "Link": "https://app.box.com/s/********************************", "SHA-1": "d45c5b2614aedcd2eafcd7bd20ab7568f001be5b", "Date": "08/03/2015", "Year": "2015"}, {"Filename": "Terracotta-VPN-Report-Final-8-3", "Title": "RSA Research Terracotta VPN: Enabler Of Advanced Threat Anonymity", "Source": "RSA", "Link": "https://app.box.com/s/********************************", "SHA-1": "e820638a0c4690636ebac596e0bbc040308aa040", "Date": "08/04/2015", "Year": "2015"}, {"Filename": "ThreatGroup-3390", "Title": "Threat Group-3390 Targets Organizations For Cyberespionage", "Source": "Dell Secureworks", "Link": "https://app.box.com/s/********************************", "SHA-1": "060957bd034772155905e49648e869f2bfc0adfb", "Date": "08/05/2015", "Year": "2015"}, {"Filename": "<PERSON><PERSON>sky_Report_Darkhotel_2015(08-10-2015)", "Title": "<PERSON><PERSON><PERSON>'s attacks in 2015 ", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "034b5ef9a0a81222ce6fec74f19884af4d02353e", "Date": "08/10/2015", "Year": "2015"}, {"Filename": "ESET_Carbanak-packing-new-guns(09-08-2015)", "Title": "Carbanak is packing new guns", "Source": "ESET", "Link": "https://app.box.com/s/********************************", "SHA-1": "608cb9d67d1afa94db103e549c2442e5e9fc7788", "Date": "09/08/2015", "Year": "2015"}, {"Filename": "F-Secure_TheDukes_whitepaper(9-17-15)", "Title": "THE DUKES: 7 years of Russian cyberespionage", "Source": "F-Secure", "Link": "https://app.box.com/s/********************************", "SHA-1": "c02195e501548fc9b8e2e13673a7e12e1af9e207", "Date": "09/17/2015", "Year": "2015"}, {"Filename": "Secureworks_HackerGroup-Creates-Network-Fake-LinkedIn-Profiles(10-07-2015)", "Title": "Hacker Group Creates Network of Fake LinkedIn Profiles", "Source": "Secureworks", "Link": "https://app.box.com/s/********************************", "SHA-1": "36fdb5f263a2abc93ab50d345d3626aded73050e", "Date": "10/07/2015", "Year": "2015"}, {"Filename": "CitizenLab_Mapping-FinFishers-Continuing-Proliferation(Oct-15-15)", "Title": "Pay No Attention to the Server Behind the Proxy: Mapping FinFisher's Continuing Proliferation", "Source": "Citizen Lab", "Link": "https://app.box.com/s/********************************", "SHA-1": "26840a8fbda17c1ae5bcb6198914ebf5e45308b2", "Date": "10/15/2015", "Year": "2015"}, {"Filename": "Checkpoint_rocket-kitten-report(Nov-9-2015)", "Title": "Rocket Kitten: A Campaign With 9 Lives", "Source": "Checkpoint", "Link": "https://app.box.com/s/********************************", "SHA-1": "523ae1be152df2a4d1de51ee2b3e7f23cad62628", "Date": "11/09/2015", "Year": "2015"}, {"Filename": "Microsoft_Security_Intelligence_Report_Volume_19_English(11-16-2015)", "Title": "Microsoft Security Intelligence Report (Volume 19)", "Source": "Microsoft", "Link": "https://app.box.com/s/********************************", "SHA-1": "666a7931d4fc2466852e0676e318391a23aec1d1", "Date": "11/16/2015", "Year": "2015"}, {"Filename": "RSA_Peering-Into-GlassRAT-final(Nov-23-15)", "Title": "PEERING INTO GLASSRAT: A Zero Detection Trojan from China", "Source": "RSA", "Link": "https://app.box.com/s/********************************", "SHA-1": "1d72a50b38ee4b5f57684726cef957f61ae6d2f2", "Date": "11/23/2015", "Year": "2015"}, {"Filename": "Symantec_Iran-based-attackers-use-back-door-threats-to-spy-on-Middle-Eastern-targets(Dec-7-2015)", "Title": "Iran-based attackers use back door threats to spy on Middle Eastern targets", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "7819b3820b7af3f3d3f0335989a95ae6314b353b", "Date": "12/07/2015", "Year": "2015"}, {"Filename": "Symantec_CadelSpy-Remexi-IOC(12-07-2015)", "Title": "Backdoor.Cadelspy and Backdoor.Remexi indicators of compromise", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "a3d9e8a14cf1729af25bf64193ac17bf9da56a20", "Date": "12/07/2015", "Year": "2015"}, {"Filename": "Kaspersky_Evolution-of-Cyber-Threats-in-the-Corporate-Sector(Dec-10-2015)", "Title": "Evolution of Cyber Threats in the Corporate Sector", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "75c2325f529d34155445af6a82bb22ec4277a80f", "Date": "12/10/2015", "Year": "2015"}, {"Filename": "Fidelis_FTA_1020_Fidelis_Inocnation_FINAL(Dec-16-15)", "Title": "Dissecting the Malware Involved in the INOCNATION Campaign", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "e10651d3a6223055e95464f0023b549cdf7ca00c", "Date": "12/16/2015", "Year": "2015"}, {"Filename": "PaloAlto_BBSRAT-Attacks-Targeting-Russian-Organizations-Linked-to-Roaming-Tiger(Dec-22-15)", "Title": "BBSRAT Attacks Targeting Russian Organizations Linked to Roaming Tiger", "Source": "Palo Alto", "Link": "https://app.box.com/s/********************************", "SHA-1": "e9de634545c873b0999728df361b28fae9536dc2", "Date": "12/22/2015", "Year": "2015"}, {"Filename": "PWC_ELISE- Security-Through-Obesity(Dec-23-15)", "Title": "ELISE: Security Through Obesity", "Source": "PWC", "Link": "https://app.box.com/s/********************************", "SHA-1": "7baa3b39f072e82717aa554f3434863f7e9edd1f", "Date": "12/23/2015", "Year": "2015"}, {"Filename": "ESET_BlackEnergy-by-the-SSHBearDoor(Jan-3-16)", "Title": "BlackEnergy by the SSHBearDoor: attacks against Ukrainian news media and electric industry", "Source": "ESET", "Link": "https://app.box.com/s/********************************", "SHA-1": "8bcb1743d4a9ddbbc6daf063dca8c8107baed218", "Date": "01/03/2016", "Year": "2016"}, {"Filename": "<PERSON>sky_Operation-<PERSON><PERSON><PERSON>_TLP_WHITE(Jan-7-2016)", "Title": "Operation Dusty Sky", "Source": "<PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "9b7bf2d6c4a10dac7483c618562e701f42c1dc32", "Date": "01/07/2016", "Year": "2016"}, {"Filename": "Clearsky_Operation-DustySky-indicators(Jan-7-2016)", "Title": "Operation Dusty Sky (indicators)", "Source": "<PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "1acdc7b846d53b582075ea4a3c5ba64bbbf10e81", "Date": "01/07/2016", "Year": "2016"}, {"Filename": "Arbor_Uncovering-the-Seven-Pointed-<PERSON><PERSON>(Jan-11-16)", "Title": "Uncovering the Seven Pointed Dagger", "Source": "Arbor Networks", "Link": "https://app.box.com/s/********************************", "SHA-1": "37584381a0a0c2d566b8ce13965ffa7226cdaa71", "Date": "01/11/2016", "Year": "2016"}, {"Filename": "<PERSON><PERSON>_<PERSON><PERSON>-in-a-<PERSON><PERSON><PERSON>(Jan-14-16)", "Title": "RESEARCH SPOTLIGHT: NEEDLES IN A HAYSTACK", "Source": "Cisco", "Link": "https://app.box.com/s/********************************", "SHA-1": "b0e8168b58ee221655df862460e305841c86f16c", "Date": "01/14/2016", "Year": "2016"}, {"Filename": "ESET_Cyberattacks-Ukrainian-power-industry(01-20-2016)", "Title": "New wave of cyberattacks against Ukrainian power industry", "Source": "ESET", "Link": "https://app.box.com/s/********************************", "SHA-1": "ed19e5c293f8acef03e89ade53f179c941155479", "Date": "01/20/2016", "Year": "2016"}, {"Filename": "PaloAlto_Scarlet-<PERSON><PERSON>(Jan-24-16)", "Title": "Scarlet <PERSON>", "Source": "Palo Alto", "Link": "https://app.box.com/s/********************************", "SHA-1": "9a7bd4348f8ad97d5440d287b81382d47f71d954", "Date": "01/24/2016", "Year": "2016"}, {"Filename": "Ka<PERSON>sky_BlackEnergy-APT-Attacks-in-Ukraine-employ-spearphishing-with-Word-documents(Jan-28-16)", "Title": "BlackEnergy APT Attacks in Ukraine employ spearphishing with Word documents", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "5780a1382be59719a980376feb783590e422b1aa", "Date": "01/28/2016", "Year": "2016"}, {"Filename": "PaloAlto_Emissary-Trojan-Changelog-Did-Operation-<PERSON>-Blossom-Cause-It-to-Evolve(Feb-03-16)", "Title": "Emissary Trojan Changelog: Did Operation Lotus Blossom Cause It To Evolve", "Source": "Palo Alto", "Link": "https://app.box.com/s/********************************", "SHA-1": "31c81ceafb1de9c731e7e003e3a638b9841a78e4", "Date": "02/03/2016", "Year": "2016"}, {"Filename": "PaloAlto_T9000-Advanced-Modular-Backdoor-Uses-Complex-Anti-Analysis-Techniques(Feb-04-16)", "Title": "T9000: Advanced Modular Backdoor Uses Complex Anti Analysis Techniques", "Source": "Palo Alto", "Link": "https://app.box.com/s/********************************", "SHA-1": "62df4dd012589910b6be8df92f23e231c493772a", "Date": "02/04/2016", "Year": "2016"}, {"Filename": "PaloAlto_Attack-on-French-Diplomat-Linked-to-Operation-<PERSON>-Blossom(Feb-08-16)", "Title": "Attack On French Diplomat Linked To Operation Lotus Blossom", "Source": "Palo Alto", "Link": "https://app.box.com/s/********************************", "SHA-1": "777fc2e7355924588a27d693cdfcc31bcf8ec76b", "Date": "02/08/2016", "Year": "2016"}, {"Filename": "ICIT-Brief-Know-Your-Enemies-2.0(02-08-2016)", "Title": "Know Your Enemies 2.0: A Primer on Advanced Persistent Threat Groups", "Source": "ICIT", "Link": "https://app.box.com/s/********************************", "SHA-1": "6cc38d03649b53205ea47fad7672a39480be93ab", "Date": "02/08/2016", "Year": "2016"}, {"Filename": "Kaspersky_Poseidon-Group(Feb-09-16)", "Title": "Poseidon Group", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "3a2dc9b54f9c5047df5162590c51e51b3392e86c", "Date": "02/09/2016", "Year": "2016"}, {"Filename": "PaloAlto_A-Look-Into-Fysbis-Sofacys-Linux-Backdoor(Feb-12-16)", "Title": "A Look Into Fysbis: Sofacy's Linux Backdoor", "Source": "Palo Alto", "Link": "https://app.box.com/s/********************************", "SHA-1": "6b6c4552509612cec438d34e58908e166b005238", "Date": "02/12/2016", "Year": "2016"}, {"Filename": "Cylance_Op_Dust_Storm_Report(Feb-23-2016)", "Title": "Operation Duststorm", "Source": "Cylance", "Link": "https://app.box.com/s/********************************", "SHA-1": "606f656561781dba6fdef666ece6a0cc24709f01", "Date": "02/23/2016", "Year": "2016"}, {"Filename": "Novetta_Operation-Blockbuster-Report(Feb-24-2016)", "Title": "Operation Blockbuster", "Source": "Novetta", "Link": "https://app.box.com/s/********************************", "SHA-1": "29d015e736a3012277f84f1b1bedc9bcac060648", "Date": "02/24/2016", "Year": "2016"}, {"Filename": "Bluecoat_FROM_SEOUL_TO_SONY(2-24-16)", "Title": " FROM SEOUL TO SONY: THE HISTORY OF THE DARKSEOUL GROUP AND THE SONY INTRUSION MALWARE DESTOVER", "Source": "Bluecoat", "Link": "https://app.box.com/s/********************************", "SHA-1": "5ec7109b992643aabb83ba6187230e3033699875", "Date": "02/24/2016", "Year": "2016"}, {"Filename": "Proofpoint_operation-transparent-tribe-threat-insight-en(Mar-01-16)", "Title": "Operation Transparent Tribe", "Source": "Proofpoint", "Link": "https://app.box.com/s/********************************", "SHA-1": "f21f1f6da5cc09b6e5542606d66f2ecd68e45abc", "Date": "03/01/2016", "Year": "2016"}, {"Filename": "TheCitizenLab_Shifting-Tactics-Tracking-changes-in-years-long-espionage-campaign-against-Tibetans(Mar-10-16)", "Title": "Shifting Tactics Tracking Changes In Years Long Espionage Campaign Against Tibetans", "Source": "Citizen Lab", "Link": "https://app.box.com/s/********************************", "SHA-1": "e39432b344d29121ec4bcb658ef5292f2416a05a", "Date": "03/10/2016", "Year": "2016"}, {"Filename": "Symantec_Suckfly-Revealing-the secret-life-of-your-code-signing-certificates(Mar-15-16)", "Title": "Suckfly: Revealing the secret life of your code signing certificates", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "4ff503930f8fd317739ca0db9c81c7d636335597", "Date": "03/15/2016", "Year": "2016"}, {"Filename": "PWC_Taiwan-Presidential-Election-A-Case-Study-on-Thematic-Targeting(Mar-17-2016)", "Title": "Taiwan Presidential Election: A Case Study on Thematic Targeting", "Source": "PWC", "Link": "https://app.box.com/s/********************************", "SHA-1": "b039fa56126220d8df26a5ad6853a884bd76581e", "Date": "03/17/2016", "Year": "2016"}, {"Filename": "Symantec_Taiwan-targeted-cyberespionage-Trojan(03-29-2016)", "Title": "Taiwan targeted with new cyberespionage back door Trojan", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "9e322f3701772b212bf17221448ef5966457ea07", "Date": "03/29/2016", "Year": "2016"}, {"Filename": "Arbor_The-Four-Element-Sword-Engagement(4-13-16)", "Title": "The Four Element Sword Engagement", "Source": "Arbor", "Link": "https://app.box.com/s/********************************", "SHA-1": "e7c914466713e7e74fcc1338398a1340464e63bc", "Date": "04/13/2016", "Year": "2016"}, {"Filename": "TheCitizenLab_Between-Hong-Kong-and-Burma_Tracking-UP007-and-SLServer-Espionage-Campaigns(4-18-16)", "Title": "Between Hong Kong and Burma: Tracking UP007 and SLServer Espionage Campaign", "Source": "Citizen Lab", "Link": "https://app.box.com/s/********************************", "SHA-1": "bbe1b72cdd7e601d5f701b5e4a03a71b776fc7fa", "Date": "04/18/2016", "Year": "2016"}, {"Filename": "TrendMicro_NetherlandsCyberAttack(04-21-2016)", "Title": "Looking Into a Cyber-Attack Facilitator in the Netherlands", "Source": "Trend Micro", "Link": "https://app.box.com/s/********************************", "SHA-1": "1b9a9fd865cc671ceef94c0ddcfbe8bb99fdc182", "Date": "04/21/2016", "Year": "2016"}, {"Filename": "TrendMicro_NetherlandsCyberAttack_Appendix(04-21-2016)", "Title": "Looking Into a Cyber-Attack Facilitator in the Netherlands (Appendix)", "Source": "Trend Micro", "Link": "https://app.box.com/s/********************************", "SHA-1": "25509ab2f9468a0a89bd62750b3549ab32d48b1a", "Date": "04/21/2016", "Year": "2016"}, {"Filename": "<PERSON><PERSON>_<PERSON>_Ghost_Dragon(04-22-2016)", "Title": "The Ghost Dragon", "Source": "Cylance", "Link": "https://app.box.com/s/********************************", "SHA-1": "8c629cb675335784dd319ac0f47822274b5d7858", "Date": "04/22/2016", "Year": "2016"}, {"Filename": "BAESystems_SSA-Two-bytes-to-951m(04-25-2016)", "Title": "Two Bytes to $951M", "Source": "BAE Systems", "Link": "https://app.box.com/s/********************************", "SHA-1": "0387df8bdfbe771ec7ee80715c58afac76266a0e", "Date": "04/25/2016", "Year": "2016"}, {"Filename": "Microsoft_Targeted-attacks-in-South-and-Southeast-Asia(Apr-26-16)", "Title": "PLATINUM Targeted attacks in South and Southeast Asia", "Source": "Microsoft", "Link": "https://app.box.com/s/********************************", "SHA-1": "ffe1ab81dc16f38930659c5ec9f6f8f9ebdf7d35", "Date": "04/26/2016", "Year": "2016"}, {"Filename": "Fidelis_Turbo-Twist-Two-64-bit-Der<PERSON>bi-Strains-Converge(May-2-16)", "Title": "Turbo Twist: Two 64-bit Derusbi Strains Converge", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "5ba6ec70bf70a31f8d61d408be83d3a999d9beee", "Date": "05/02/2016", "Year": "2016"}, {"Filename": "PaloAlto_PrinceofPersiaInfyMalware(05-02-2016)", "Title": "Prince of Persia: Infy Malware Active In Decade of Targeted Attacks", "Source": "Palo Alto", "Link": "https://app.box.com/s/********************************", "SHA-1": "bd91ba55a44f3288be1483e8d160e2910e1eed21", "Date": "05/02/2016", "Year": "2016"}, {"Filename": "PwC_Exploring_CVE-2015-2545(05-06-2016)", "Title": "Exploring CVE-2015-2545 and its users", "Source": "PWC", "Link": "https://app.box.com/s/********************************", "SHA-1": "2b7257511cc403a0a5f9d306663fbbe6c7c1c797", "Date": "05/06/2016", "Year": "2016"}, {"Filename": "Fox-IT_mofang_threatreport_tlp-white(05-17-2016)", "Title": "Mofang: A politically motivated information stealing adversary", "Source": "Fox-IT", "Link": "https://app.box.com/s/********************************", "SHA-1": "ffa66796bd7c7f077c31285e6563ccb522b7e9b1", "Date": "05/17/2016", "Year": "2016"}, {"Filename": "ESET_Operation-Groundbait(5-17-16)", "Title": "Operation Groundbait:Analysis of a surveillance toolkit", "Source": "ESET", "Link": "https://app.box.com/s/********************************", "SHA-1": "5938181ed1aec8281f229581f38f37f78eb24821", "Date": "05/17/2016", "Year": "2016"}, {"Filename": "Symantec_Indian-organizations-targeted-in-Suckfly-attacks(5-17-16)", "Title": "Indian organizations targeted in Suckfly attacks", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "fbf36d88f19b64802a9b1e31e7b8554b8e33be37", "Date": "05/17/2016", "Year": "2016"}, {"Filename": "TrendMicro_Operation-C-Major_blog(5-18-16)", "Title": "Operation C-Major Actors Also Used Android BlackBerry Mobile Spyware Against Targets", "Source": "Trend Micro", "Link": "https://app.box.com/s/********************************", "SHA-1": "d4203fd526a2ba2714bc19f62e24e8ef91806b1a", "Date": "05/18/2016", "Year": "2016"}, {"Filename": "McAfee_SWIFT-Insider-Knowledge(05-20-2016)", "Title": "Attacks on SWIFT Banking System Benefit From Insider Knowledge", "Source": "McAfee", "Link": "https://app.box.com/s/********************************", "SHA-1": "abe3c0fd4fb29ba970c896d542fe8e9e4edf5f15", "Date": "05/20/2016", "Year": "2016"}, {"Filename": "FireEye_Targeted-Attacks-against-Banks-in-the-Middle-East(5-23-16)", "Title": "Targeted Attacks against Banks in the Middle East", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "66abb93ddb5a1e2dc75b1d8db5c97417200c3996", "Date": "05/23/2016", "Year": "2016"}, {"Filename": "GovCERTch_Report_Ruag_Espionage_Case(5-23-16)", "Title": "APT Case RUAG Technical Report", "Source": "GovCERT.ch", "Link": "https://app.box.com/s/********************************", "SHA-1": "dbee63b0f8cd29139b4d12b867e3a80de139c0df", "Date": "05/23/2016", "Year": "2016"}, {"Filename": "PaloAlto_ke3chang_tidepool(5-23-2016)", "Title": "Operation Ke3chang Resurfaces With New TidePool Malware", "Source": "Palo Alto", "Link": "https://app.box.com/s/********************************", "SHA-1": "2c09f13be3c035ec932d6c91366f61f7f1320f72", "Date": "05/23/2016", "Year": "2016"}, {"Filename": "PaloAlto_New- Wekby-Attacks-Use- DNS-Requests- As- Command-and- Control- Mechanism(5-24-16)", "Title": "New Wekby Attacks Use DNS Requests As Command and Control Mechanism", "Source": "Palo Alto", "Link": "https://app.box.com/s/********************************", "SHA-1": "cfea29fab43ff53f7869542633891a89ead24a5f", "Date": "05/24/2016", "Year": "2016"}, {"Filename": "Kaspersky_CVE-2015-2545_overview-of-current-threats(5-25-16)", "Title": "CVE-2015-2545: overview of current threats", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "5f77c03a83f36e0f74a37efea8cc959613ee3d10", "Date": "05/25/2016", "Year": "2016"}, {"Filename": "Symantec_SWIFT-malware-linked-financial-attacks(05-26-2016)", "Title": "SWIFT attackers' malware linked to more financial attacks", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "b0c56ff2ab4614632c008d3384d39764e4cf5664", "Date": "05/26/2016", "Year": "2016"}, {"Filename": "TrendMicro_IXESHE_IHEATE(05-27-2016)", "Title": "IXESHE Derivative IHEATE Targets Users in America", "Source": "Trend Micro", "Link": "https://app.box.com/s/********************************", "SHA-1": "d611ca60c87498c89b450dda0f06a3cffebe53ae", "Date": "05/27/2016", "Year": "2016"}, {"Filename": "CitizenLab-<PERSON><PERSON><PERSON><PERSON><PERSON>(05-29-2016)", "Title": "Stealth Falcon", "Source": "Citizen Lab", "Link": "https://app.box.com/s/********************************", "SHA-1": "6060d8839dc4054fd3d57b2935eb79b2876f75d6", "Date": "05/29/2016", "Year": "2016"}, {"Filename": "FireEye_IRONGATE_ICS(06-02-2016)", "Title": "IRONGATE ICS Malware: Nothing to See Here...Masking Malicious Activity on SCADA Systems", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "78ea4b8589b7b776b4a9fe94fd42bd74740d4276", "Date": "06/02/2016", "Year": "2016"}, {"Filename": "FireEye_IndianGovSpearPhish(06-03-2016)", "Title": "APT Group Sends <PERSON><PERSON> Phishing Emails to Indian Government Officials", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "97a52c09444fff77e5b7dc8e1cc3606bed224ace", "Date": "06/03/2016", "Year": "2016"}, {"Filename": "<PERSON><PERSON>_<PERSON><PERSON>-<PERSON><PERSON>-Indian-Government-Officials(06-03-2016)", "Title": "Apt Group Sends Spear Phishing Emails To Indian Government Officials", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "3108aea5c7ef8c8a6d3633612c4f0231b1cec25b", "Date": "06/03/2016", "Year": "2016"}, {"Filename": "CrowdStrike_BearsintheMidst_DNC(06-04-2016)", "Title": "Bears in the Midst: Intrusion into the Democratic National Committee", "Source": "Crowdstrike", "Link": "https://app.box.com/s/********************************", "SHA-1": "269f0dfade9d5929e678027bd1c274e45d851b45", "Date": "06/04/2016", "Year": "2016"}, {"Filename": "Clearsky_Operation-DustySky2(6-9-16)", "Title": "Operation DustySky Part 2", "Source": "<PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "a00dc576f03496351c6ec2989aac2a26891c4cff", "Date": "06/09/2016", "Year": "2016"}, {"Filename": "Clearsky_Operation-DustySky2-indicators(6-9-16)", "Title": "Operation DustySky Part 2 Indicators", "Source": "<PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "d14171df6033ec8c090de678e505d08c5f62fd88", "Date": "06/09/2016", "Year": "2016"}, {"Filename": "Microsoft_DUBNIUM(06-09-2016)", "Title": "Reverse-engineering DUBNIUM", "Source": "Microsoft", "Link": "https://app.box.com/s/********************************", "SHA-1": "aa49d07c40e3bf8da6779a1d08eeda6efcce3706", "Date": "06/09/2016", "Year": "2016"}, {"Filename": "PaloAlto_SofacyUSGov(06-14-2016)", "Title": "New Sofacy Attacks Against US Government Agency", "Source": "Palo Alto", "Link": "https://app.box.com/s/********************************", "SHA-1": "61c759fab05cb341d84f825452ad17cbcfb23c31", "Date": "06/14/2016", "Year": "2016"}, {"Filename": "Citizenlab_Group5_Syria_Iranian_Connection(06-14-2016)", "Title": "Group5: Syria and the Iranian Connection", "Source": "Citizen Lab", "Link": "https://app.box.com/s/********************************", "SHA-1": "c9d42c2c21c439babfc553984e4b71f6c1db7afb", "Date": "06/14/2016", "Year": "2016"}, {"Filename": "Secureworks_TG-4127<PERSON><PERSON><PERSON>(06-16-2016)", "Title": "Threat Group-4127 Targets Hillary <PERSON> Presidential Campaign", "Source": "Secureworks", "Link": "https://app.box.com/s/********************************", "SHA-1": "4827ac42240efd00e38b61e68f85379adb760861", "Date": "06/16/2016", "Year": "2016"}, {"Filename": "DellSecureworks_ThreatGroup-4127-Targets-Clinton-Campaign(06-16-2016)", "Title": "Threat Group 4127 Targets Hillary <PERSON> Presidential Campaign", "Source": "Dell Secureworks", "Link": "https://app.box.com/s/********************************", "SHA-1": "9f1be920242bc0b1cca697c0b5034c24a9e9c0a9", "Date": "06/16/2016", "Year": "2016"}, {"Filename": "Ka<PERSON>sky_ScarCruft-OpDaybreak(06-17-2016)", "Title": "Flash zero-day exploit deployed by the ScarCruft APT Group", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "fb7781a1552ee8fcddd13fc560aa1dea9ad4955a", "Date": "06/17/2016", "Year": "2016"}, {"Filename": "Kaspersky_Flash-zero-day-ScarCruft-APT-Group(06-17-2016)", "Title": "Operation Daybreak", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "257650cc95d4f77ec70553ce9bbf0a4e393ea4e3", "Date": "06/17/2016", "Year": "2016"}, {"Filename": "Microsoft_RE-DUBNIUM-FlashExploit(06-20-2016)", "Title": "Reverse-engineering DUBNIUM's Flash-targeting exploit", "Source": "Microsoft", "Link": "https://app.box.com/s/********************************", "SHA-1": "4380f06f404326f756a7c48cb7dd0236e1934cb0", "Date": "06/20/2016", "Year": "2016"}, {"Filename": "Fidelis_DNC-IntrusionMalware(06-20-2016)", "Title": "Findings from Analysis of DNC Intrusion Malware", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "d093215af4ce896149f0fc7a42eacad89f58ac3c", "Date": "06/20/2016", "Year": "2016"}, {"Filename": "Fireeye-rpt-china-espionage(06-20-2016)", "Title": "Red Line Drawn: China Recalculates Its Use Of Cyber Espionage", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "4961a742b4c650d3fb84f9321de52911df176f1b", "Date": "06/20/2016", "Year": "2016"}, {"Filename": "ESET-<PERSON>_The_Bear_Den(6-21-2016)", "Title": "Visiting The Bear Den A Journey in the Land of (Cyber-)Espionage", "Source": "ESET", "Link": "https://app.box.com/s/********************************", "SHA-1": "98985054ab8d3b6c232132f1957fefc4ba01c7a6", "Date": "06/21/2016", "Year": "2016"}, {"Filename": "PaloAlto_TrackingElirksJapanSimilaritiesPreviousAttacks(06-23-2016)", "Title": "Tracking Elirks Variants in Japan: Similarities to Previous Attacks", "Source": "Palo Alto", "Link": "https://app.box.com/s/********************************", "SHA-1": "eba3ad81b20ed7b9ef081714928ecf1a4324b057", "Date": "06/23/2016", "Year": "2016"}, {"Filename": "Secureworks_ThreatGroup-4127-Targets-Google-Accounts(06-26-2016)", "Title": "Threat Group-4127 Targets Google Accounts", "Source": "Secureworks", "Link": "https://app.box.com/s/********************************", "SHA-1": "4b340fd7ca63937db459345ebbf915a9dd5cfe01", "Date": "06/26/2016", "Year": "2016"}, {"Filename": "PaloAlto_PrinceofPersiaGameOver(06-28-2016)", "Title": "Prince of Persia Game Over", "Source": "Palo Alto", "Link": "https://app.box.com/s/********************************", "SHA-1": "3f92bfbfdb0fee7eda8613fc3a6ff515ffceb972", "Date": "06/28/2016", "Year": "2016"}, {"Filename": "JPCERT_AsruexShortcutFiles(06-30-2016)", "Title": "Asruex: Malware Infecting through Shortcut Files", "Source": "JPCERT", "Link": "https://app.box.com/s/********************************", "SHA-1": "0ddd9b23aaa773615e7d6392969d40b332e0c85f", "Date": "06/30/2016", "Year": "2016"}, {"Filename": "Bitdefender_Pacifier-APT(7-1-2016)", "Title": "Pacifier APT", "Source": "Bitdefender", "Link": "https://app.box.com/s/********************************", "SHA-1": "6289dff2cbd2750c76517007989483922179fa40", "Date": "07/01/2016", "Year": "2016"}, {"Filename": "ESET_targeting-Central-and-EasternEurope(07-01-2016)", "Title": "Espionage toolkit targeting Central and Eastern Europe uncovered", "Source": "ESET", "Link": "https://app.box.com/s/********************************", "SHA-1": "8d9af4bb8b4bafcb9e58ab21e1419abd2eed46b2", "Date": "07/01/2016", "Year": "2016"}, {"Filename": "Cymmetria_Unveiling-Patchwork(07-07-16)", "Title": "Unveiling Patchwork the Copy Paste APT", "Source": "Cymmetria", "Link": "https://app.box.com/s/********************************", "SHA-1": "955ddb4453827e2c1664f2924e75b21fb0c0496d", "Date": "07/07/2016", "Year": "2016"}, {"Filename": "Proofpoint_NetTraveler-TargetsRussianEuropean(07-07-2016)", "Title": "NetTraveler APT Targets Russian, European Interests", "Source": "ProofPoint", "Link": "https://app.box.com/s/********************************", "SHA-1": "d647ecd9a694447bc8ee9096f425ba29d93f7fb7", "Date": "07/07/2016", "Year": "2016"}, {"Filename": "<PERSON><PERSON><PERSON>_DroppingElephant(07-08-2016)", "Title": "The Dropping Elephant - aggressive cyber-espionage in the Asian region", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "e0606a9e6a785d942b50d281d87550dc03d3666e", "Date": "07/08/2016", "Year": "2016"}, {"Filename": "Symantec_Patchwork-expands-to-industries(07-25-2016)", "Title": "Patchwork cyberespionage group expands targets from governments to wide range of industries", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "0184ab60e9f8f38cd59f0a53278192a286255179", "Date": "07/25/2016", "Year": "2016"}, {"Filename": "EFF_OperationManul(08-03-2016)", "Title": "Operation Manul", "Source": "EFF", "Link": "https://app.box.com/s/********************************", "SHA-1": "cf6fad700146b469d54f47c1541a84bb0dc08bc6", "Date": "08/03/2016", "Year": "2016"}, {"Filename": "Forcepoint_Moonsoon(08-06-2016)", "Title": "Moonsoon - Analysis of an APT Campaign", "Source": "Forcepoint", "Link": "https://app.box.com/s/********************************", "SHA-1": "b910f06ecd66d0a297e2043369b82a29cf770eee", "Date": "08/06/2016", "Year": "2016"}, {"Filename": "Symantec_Strider-group-turns-eye-targets(08-07-2016)", "Title": "Strider: Cyberespionage group turns eye of Sauron on targets", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "2954f154b324e61eae407e7d656a5b4b373fb5cb", "Date": "08/07/2016", "Year": "2016"}, {"Filename": "<PERSON><PERSON><PERSON>_The-ProjectSauron-APT_research_KL(08-08-2016)", "Title": "The ProjectSauron APT", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "877d7520e4b6fd17e48903f779a48b8619ebe87e", "Date": "08/08/2016", "Year": "2016"}, {"Filename": "Visa_Oracle-Micros-Compromise(08-08-2016)", "Title": "Carbanak Oracle Breach", "Source": "Visa", "Link": "https://app.box.com/s/********************************", "SHA-1": "8dcd6158f82898310af73d9fbeae8e626dbfdb7b", "Date": "08/08/2016", "Year": "2016"}, {"Filename": "<PERSON><PERSON><PERSON><PERSON>_Carbanak-Oracle-breach(08-13-2016)", "Title": "Visa Alert and Update on the Oracle Breach", "Source": "<PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "af70e2e4750a95aa3748fc15e3c1fb92f1b47e29", "Date": "08/13/2016", "Year": "2016"}, {"Filename": "Citizenlab_NSO_iPhone_ZeroDays_UAE(08-24-2016)", "Title": "The Million Dollar Dissident: NSO Group's iPhone Zero-Days used against a UAE Human Rights Defender", "Source": "Citizen Lab", "Link": "https://app.box.com/s/********************************", "SHA-1": "32c2e322e6e97466dc82e81fc94acc14eee10192", "Date": "08/24/2016", "Year": "2016"}, {"Filename": "Symantec_Buckeye_HongKong(09-06-2016)", "Title": "Buckeye cyberespionage group shifts gaze from US to Hong Kong", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "36cc8f9d42465b2ac2681772862e21ecb2eaa137", "Date": "09/06/2016", "Year": "2016"}, {"Filename": "Cyberkov-Hunting-Libyan-Scorpions-EN(9-18-16)", "Title": "Hunting Libyan Scorpions", "Source": "Cyberkov Security", "Link": "https://app.box.com/s/********************************", "SHA-1": "9030bce0306febd7e94fd047d480512583889389", "Date": "09/18/2016", "Year": "2016"}, {"Filename": "PaloAlto_Sofacys_Komplex_OSXTrojan(09-26-2016)", "Title": "Sofacy's Komplex OS X Trojan", "Source": "Palo Alto", "Link": "https://app.box.com/s/********************************", "SHA-1": "fb15f1de386ed3102956ff732b50c35ef56548b5", "Date": "09/26/2016", "Year": "2016"}, {"Filename": "ThreatConnect_Belling_the_BEAR(09-28-2016)", "Title": "Belling the BEAR", "Source": "ThreatConnect", "Link": "https://app.box.com/s/********************************", "SHA-1": "802cfc799eb72c9b5e40ca56e724c75b1fa10be8", "Date": "09/28/2016", "Year": "2016"}, {"Filename": "<PERSON><PERSON><PERSON>_StrongPity-Waterhole-Targeting-Italian-Belgian-Encryption-Users(10-03-2016)", "Title": "On the StrongPity Waterhole Attacks Targeting Italian and Belgian Encryption Users", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "f4c3883bdb12af3225b3b57f2d8352cb37586312", "Date": "10/03/2016", "Year": "2016"}, {"Filename": "<PERSON><PERSON><PERSON>_Wave-your-false-flags(10-05-2016)", "Title": "Wave your false flags! Deception tactics muddying attribution in targeted attacks", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "12ecd2809c35c574672cb2b1b22323aa9de1d69a", "Date": "10/05/2016", "Year": "2016"}, {"Filename": "VirusBulletin_EvronRaz(10-05-2016)", "Title": "Apt Reports And Opsec Evolution, Or: These Are Not The Apt Reports You Are Looking For", "Source": "Virus Bulletin", "Link": "https://app.box.com/s/********************************", "SHA-1": "5801fcf1d7fd14cdf2267fd5abac67a448812677", "Date": "10/05/2016", "Year": "2016"}, {"Filename": "eset-sednit-part1(10-20-2016)", "Title": "En Route with Sednit Part 1: Approaching the Target", "Source": "ESET", "Link": "https://app.box.com/s/********************************", "SHA-1": "aa59479dccb863926ace7d5a6d87e356eb9dad4a", "Date": "10/20/2016", "Year": "2016"}, {"Filename": "eset-sednit-part2(10-25-2016)", "Title": "En Route with Sednit Part 2: Observing the Comings and Goings", "Source": "ESET", "Link": "https://app.box.com/s/********************************", "SHA-1": "6f1dfb7fc2367f5787b076deb44f37704a682caa", "Date": "10/25/2016", "Year": "2016"}, {"Filename": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Ho<PERSON>-Magic-Reappearance(10-25-2016)", "Title": "<PERSON><PERSON><PERSON>'s Magic Reappearance", "Source": "Palo Alto Networks", "Link": "https://app.box.com/s/********************************", "SHA-1": "242a98ec10de9c8044a355cbac843987d891b264", "Date": "10/25/2016", "Year": "2016"}, {"Filename": "VectraNetworks_Moonlight-Targeted-attacks-MiddleEast(10-26-2016)", "Title": "Moonlight - Targeted attacks in the Middle East", "Source": "Vectra Networks", "Link": "https://app.box.com/s/********************************", "SHA-1": "2ad4ca5919036c23d608723cebd6eca8cd6d7240", "Date": "10/26/2016", "Year": "2016"}, {"Filename": "Forcepoint_BITTER-Targeted-attack-Pakistan(10-26-2016)", "Title": "BITTER: A Targeted attack against Pakistan", "Source": "Forcepoint", "Link": "https://app.box.com/s/********************************", "SHA-1": "385ba37be4d0617780a137a81d6593554f0ecc98", "Date": "10/26/2016", "Year": "2016"}, {"Filename": "eset-sednit-part3(10-27-2016)", "Title": "En Route with Sednit Part 3: A Mysterious Downloader", "Source": "ESET", "Link": "https://app.box.com/s/********************************", "SHA-1": "2cd04b3b878180782e8467c2cbe69d301a0d98ec", "Date": "10/27/2016", "Year": "2016"}, {"Filename": "TrendMicro_BLACKGEAR-Espionage-Campaign(10-27-2016)", "Title": "BLACKGEAR Espionage Campaign Evolves, Adds Japan To Target List", "Source": "Trend Micro", "Link": "https://app.box.com/s/********************************", "SHA-1": "bd52c0809e379a7dabdb35fcbb07d077f10a6edc", "Date": "10/27/2016", "Year": "2016"}, {"Filename": "BoozAllen_ukraine-report-when-the-lights-went-out(11-3-2016)", "Title": "When The Lights Went Out: Ukraine Cybersecurity Threat Briefing", "Source": "<PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "6a6ad533b71fd64fc9dd4948e920a0992f637302", "Date": "11/03/2016", "Year": "2016"}, {"Filename": "Volexity_Powerduke-Widespread-PostElection-Spear-<PERSON><PERSON>(11-09-2016)", "Title": "PowerDuke: Widespread Post-Election Spear Phishing Campaigns Targeting Think Tanks and NGOs", "Source": "Volexity", "Link": "https://app.box.com/s/********************************", "SHA-1": "d1e4a9dc8b5bd3aaa06b40d5fe8fcefb5328e157", "Date": "11/09/2016", "Year": "2016"}, {"Filename": "Trustwave_Carbanak _Anunak_Attack_Methodology(11-14-2016)", "Title": "New Carbanak / Anunak Attack Methodology", "Source": "Trustwave", "Link": "https://app.box.com/s/********************************", "SHA-1": "35f1522128ae21490cc812f5929b961fe0cbdf5c", "Date": "11/14/2016", "Year": "2016"}, {"Filename": "Citizenlab_KeyBoy-targeting-Tibetan-Community(11-17-2016)", "Title": "It's Parliamentary: Key<PERSON>oy and the targeting of the Tibetan Community", "Source": "Citizen Lab", "Link": "https://app.box.com/s/********************************", "SHA-1": "1c664e6ab6a1e36fb0a1dccb231acfb26e943b19", "Date": "11/17/2016", "Year": "2016"}, {"Filename": "Cysinfo_NIC-CyberSecurity-Themed-Spear-Phishing-Target-India(11-30-2016)", "Title": "Malware Actors Using Nic Cyber Security Themed Spear Phishing To Target Indian Government Organizations", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "****************************************", "Date": "11/30/2016", "Year": "2016"}, {"Filename": "Microsoft_SIR-Vol21-PROMETHIUM-NEODYMIUM-Updated(12-14-2016)", "Title": "PROMETHIUM and NEODYMIUM: Parallel zero-day attacks targeting individuals in Europe", "Source": "Microsoft", "Link": "https://app.box.com/s/********************************", "SHA-1": "31b0c92ff842596ed58185bb84c7dc4542059e85", "Date": "12/14/2016", "Year": "2016"}, {"Filename": "PaloAlto_Sofacy-DealersChoice-Attacks(12-15-2016)", "Title": "Let It Ride: The Sofacy Group's DealersChoice Attacks Continue", "Source": "Palo Alto Networks", "Link": "https://app.box.com/s/********************************", "SHA-1": "706ee4a2d806ebd510a7d0bf3640b6d7f6da74fc", "Date": "12/15/2016", "Year": "2016"}, {"Filename": "Crowdstrike_DangerClose-FancyBear-Tracking-Ukrainian-FieldArtilleryUnits(12-21-2016)", "Title": "Danger Close: <PERSON><PERSON> Tracking of Ukrainian Field Artillery Units", "Source": "Crowdstrike", "Link": "https://app.box.com/s/********************************", "SHA-1": "860387572ad036bfde33775ee89e7d92fa5d0aae", "Date": "12/21/2016", "Year": "2016"}, {"Filename": "Crowdstrike_FancyBearTracksUkrainianArtillery(12-22-2016)", "Title": "Use of Fancy Bear Android Malware tracking of Ukrainian Artillery Units", "Source": "Crowdstrike", "Link": "https://app.box.com/s/********************************", "SHA-1": "31fff8f0aab57b8edf24a77d7be9c10375ace768", "Date": "12/22/2016", "Year": "2016"}, {"Filename": "tr1adx_Bear-Hunting-APT28-Tracking(12-28-2016)", "Title": "Bear Hunting Season: Tracking APT28 ", "Source": "tr1adx", "Link": "https://app.box.com/s/********************************", "SHA-1": "692ba080ccce9cb14121d88225dcae18a3149a8a", "Date": "12/28/2016", "Year": "2016"}, {"Filename": "USCERT_GRIZZLY STEPPE(12-29-2016)", "Title": "GRIZZLY STEPPE - Russian Malicious Cyber Activity", "Source": "US-CERT", "Link": "https://app.box.com/s/********************************", "SHA-1": "6c167fbcf1f24da37ded712e81f46cb82acdc3c0", "Date": "12/29/2016", "Year": "2016"}, {"Filename": "tr1adx_Digital-Plagarist-Carbanak(01-01-2017)", "Title": "The Digital Plagiarist Campaign: TelePorting the Carbanak Crew to a New Dimension", "Source": "tr1adx", "Link": "https://app.box.com/s/********************************", "SHA-1": "b7dc94c416b4e9c080448f76a9cfbdfd211c1a8c", "Date": "01/01/2017", "Year": "2017"}, {"Filename": "<PERSON>sky_Iranian-OilRig-Delivers-Signed-Oxford(01-05-2017)", "Title": "Iranian Threat Agent OilRig Delivers Digitally Signed Malware, Impersonates University of Oxford", "Source": "<PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "37b20be7b373b82bda3d72b17b21e2e41feba382", "Date": "01/05/2017", "Year": "2017"}, {"Filename": "PaloAlgo-DragonOK-Updates-Tools-Targets-Multiple-Regions(01-05-2017)", "Title": "DragonOK Updates Toolset and Targets Multiple Geographic Regions", "Source": "Palo Alto Networks", "Link": "https://app.box.com/s/********************************", "SHA-1": "e093f2ddb288f71bdd0d8b7652b8fd43934aa358", "Date": "01/05/2017", "Year": "2017"}, {"Filename": "Forcepoint-<PERSON><PERSON><PERSON>-Fileless-Returns-BigBoss-<PERSON><PERSON><PERSON><PERSON><PERSON>(01-05-2017)", "Title": "Mm Core In-Memory Backdoor Returns As Bigboss And <PERSON><PERSON><PERSON><PERSON>", "Source": "Forcepoint", "Link": "https://app.box.com/s/********************************", "SHA-1": "b62c407ee38c26946eff590f87a8ea186865a9a9", "Date": "01/05/2017", "Year": "2017"}, {"Filename": "UnitedStates_-Senate_Committee_-on_Armed_Services-<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>(01-05-2017)", "Title": "Foreign Cyber Threats to the United States", "Source": "US Senate Committee on Armed Services", "Link": "https://app.box.com/s/********************************", "SHA-1": "a2c34f3b86cabbe9b99fb5456b763be432ac83c9", "Date": "01/05/2017", "Year": "2017"}, {"Filename": "FireEye_APT28-Center-of-Storm(01-11-2017)", "Title": "At the Center of the Storm: Russia's APT28 Strategically Evolves its Cyber Operations", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "f5eb271671c7a2def034329d77843ac296266b0c", "Date": "01/11/2017", "Year": "2017"}, {"Filename": "tr1adx_Dope-Story-Bears(01-14-2017)", "Title": "A Pretty Dope Story About Bears: Early Indicators of Continued World Anti-Doping Agency (WADA) Targeting", "Source": "tr1adx", "Link": "https://app.box.com/s/********************************", "SHA-1": "798348eb3a5019929eab49cafd36e67617970b78", "Date": "01/14/2017", "Year": "2017"}, {"Filename": "tr1adx-Bear-Spotting-Vol1(01-15-2017)", "Title": "Bear Spotting Vol. 1: Russian Nation State Targeting of Government and Military Interests", "Source": "tr1adx", "Link": "https://app.box.com/s/********************************", "SHA-1": "80f2bd5c1796cf0980515170da333662ff58a992", "Date": "01/15/2017", "Year": "2017"}, {"Filename": "Cysinfo-Uri-KashmirProtest-Phishing-targeting-Indian-Embassies(01-19-2017)", "Title": "URI Terror Attack & Kashmir Protest Themed Spear Phishing Emails Targeting Indian Embassies And Indian Ministry Of External Affairs", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "479a70bbfa14396cebb0e4768c8521a53b899337", "Date": "01/19/2017", "Year": "2017"}, {"Filename": "Citizenlab_NilePhish-Large-Scale-Targeting-Egyptian(02-02-2017)", "Title": "Nile Phish: Large-Scale Phishing Campaign Targeting Egyptian Civil Society", "Source": "Citizen Lab", "Link": "https://app.box.com/s/********************************", "SHA-1": "7a53a9ae6b4445a626e1c2d642703aaa3f01af97", "Date": "02/02/2017", "Year": "2017"}, {"Filename": "Badcyber_Polish-banks-hacked-information-stolen-unknown-attackers(02-03-2017)", "Title": "Several Polish banks hacked, information stolen by unknown attackers", "Source": "Badcyber", "Link": "https://app.box.com/s/********************************", "SHA-1": "2b5d73d85ea4173b9fa128f351bfaea0f778e52f", "Date": "02/03/2017", "Year": "2017"}, {"Filename": "kingslayer-a-supply-chain-attack(02-03-2017)", "Title": "KingSlayer A Supply chain attack", "Source": "RSA", "Link": "https://app.box.com/s/********************************", "SHA-1": "74962bac9526b55dfcd0c6690b2980f7483e587b", "Date": "02/03/2017", "Year": "2017"}, {"Filename": "US-CERT-AR-17-20045_Enhanced_Analysis_of_GRIZZLY_STEPPE_Activity(02-10-2017)", "Title": "Enhanced Analysis of GRIZZLY STEPPE Activity", "Source": "US-CERT", "Link": "https://app.box.com/s/********************************", "SHA-1": "7f8c3d1d34755bd52f850bad0bc2dc1db6783661", "Date": "02/10/2017", "Year": "2017"}, {"Filename": "Cysinfo-Targeting-Indian-Navys-Submarine-Warship-Manufacturer(02-10-2017)", "Title": "Cyber Attack Targeting Indian Navy's Submarine And Warship Manufacturer", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "619ad9c2d675c411b5c18457546aa7bcc58b31a7", "Date": "02/10/2017", "Year": "2017"}, {"Filename": "BAESystems_Lazarus-Watering-hole-attacks(02-12-2017)", "Title": "Lazarus & Watering-Hole Attacks", "Source": "BAE Systems", "Link": "https://app.box.com/s/********************************", "SHA-1": "fddefc914c62f4886d6ce9b889a3fbe8769fa886", "Date": "02/12/2017", "Year": "2017"}, {"Filename": "PaloAlto_MagicHound-Campaign-Attacks-SaudiTargets(02-15-2017)", "Title": "Magic Hound Campaign Attacks Saudi Targets", "Source": "Palo Alto Networks", "Link": "https://app.box.com/s/********************************", "SHA-1": "8675bdc6c056bf1b8893b60ecda6aa7e75ca550f", "Date": "02/15/2017", "Year": "2017"}, {"Filename": "Secureworks_Iranian-PupyRAT-Middle-Eastern-Organizations(02-15-2017)", "Title": "Iranian PupyRAT Bites Middle Eastern Organizations", "Source": "Secureworks", "Link": "https://app.box.com/s/********************************", "SHA-1": "fd572a383bb94164cb8acb74a4aa8d5beb3b6afb", "Date": "02/15/2017", "Year": "2017"}, {"Filename": "IBM_Full-<PERSON><PERSON><PERSON>(02-15-2017)", "Title": "The Full Shamoon: How the Devastating Malware Was Inserted Into Networks", "Source": "IBM", "Link": "https://app.box.com/s/********************************", "SHA-1": "21cd740f556c3aded545b3de39071ce066c612f6", "Date": "02/15/2017", "Year": "2017"}, {"Filename": "Cyberx_Operation-BugDrop(02-15-2017)", "Title": "Operation Bugdrop: Cyberx Discovers Large-Scale Cyber-Reconnaissance Operation Targeting Ukrainian Organizations", "Source": "CyberX", "Link": "https://app.box.com/s/********************************", "SHA-1": "e5395ae3b48658a35c56df32d378ee4afab3ced6", "Date": "02/15/2017", "Year": "2017"}, {"Filename": "Lookout_ViperRAT-IDF(02-16-2017)", "Title": "ViperRAT: The mobile APT targeting the Israeli Defense Force that should be on your radar", "Source": "Lookout", "Link": "https://app.box.com/s/********************************", "SHA-1": "f05008438263f72d69a2ad48ef831832f1bf0072", "Date": "02/16/2017", "Year": "2017"}, {"Filename": "Kaspersky_Breaking-Weakest-Link-IDF(02-16-2017)", "Title": "Breaking The Weakest Link Of The Strongest Chain", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "c0017fe5d8637b3848db4e8783cb05aeefd152ca", "Date": "02/16/2017", "Year": "2017"}, {"Filename": "JPCERT_Ch<PERSON>hes-CampC-<PERSON><PERSON>-Headers(02-17-2017)", "Title": "ChChes - Malware that Communicates with C&C Servers Using <PERSON><PERSON>ers", "Source": "JPCERT", "Link": "https://app.box.com/s/********************************", "SHA-1": "eeba6bed9a01db3c222e37438c5ea519eee7d617", "Date": "02/17/2017", "Year": "2017"}, {"Filename": "BAESystems_Lazarus-FalseFlag-Malware(02-20-2017)", "Title": "Lazarus' False Flag Malware", "Source": "BAE Systems", "Link": "https://app.box.com/s/********************************", "SHA-1": "4068d909a958924e1d908f6adc21ea998d1eb891", "Date": "02/20/2017", "Year": "2017"}, {"Filename": "ArborNetworks_Additional-Insights-Shamoon2(02-21-2017)", "Title": "Additional Insights on Shamoon2", "Source": "Arbor Networks", "Link": "https://app.box.com/s/********************************", "SHA-1": "e62338f92ff8ca0d7d6ac031e9a87c46d1a47a66", "Date": "02/21/2017", "Year": "2017"}, {"Filename": "Fireeye_SpearPhishing-Targeting-Mongolian-Government(02-22-2017)", "Title": "Spear Phishing Techniques Used in Attacks Targeting the Mongolian Government", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "7cb839427c1ed6c815f18b2677b905128b34e09c", "Date": "02/22/2017", "Year": "2017"}, {"Filename": "Bitdefender-Whitepaper-APT-Mac-A4-en-EN-web(02-23-2017)", "Title": "Dissecting the APT28 Mac OS X Payload", "Source": "Bitdefender", "Link": "https://app.box.com/s/********************************", "SHA-1": "d206b485a42c611ed9fe6a0b1a3ed086e954bd30", "Date": "02/23/2017", "Year": "2017"}, {"Filename": "PaloAlto_Gamaredon-Toolset-Evolution(02-27-2017)", "Title": "The Gamaredon Group Toolset Evolution", "Source": "Palo Alto Networks", "Link": "https://app.box.com/s/********************************", "SHA-1": "ea8659fb295612391dde5a4082fb106f677e48be", "Date": "02/27/2017", "Year": "2017"}, {"Filename": "Cylance_DeceptionProject-New-Japanese-Centric-Threat(02-27-2017)", "Title": "The Deception Project: A New Japanese-Centric Threat", "Source": "Cylance", "Link": "https://app.box.com/s/********************************", "SHA-1": "0b30c4bd931d88806fcbd98cb740e5b111fc8f42", "Date": "02/27/2017", "Year": "2017"}, {"Filename": "<PERSON><PERSON><PERSON>_Report_Shamoon_StoneDrill_final(03-06-2017)", "Title": "From Shamoon to StoneDrill", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "59a92a3bd0a359bb6353578ab1a241fc50529225", "Date": "03/06/2017", "Year": "2017"}, {"Filename": "FireEye-FIN7-<PERSON><PERSON><PERSON><PERSON><PERSON>-Targets-SEC-Filings(03-07-2017)", "Title": "FIN7 Spear Phishing Campaign Targets Personnel Involved in SEC Filings", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "ee0ebdcf2454d62dc23d604c472d3112a57274c6", "Date": "03/07/2017", "Year": "2017"}, {"Filename": "ClearSky_Operation-Electric-Powder-targeting-Israel-Electric-Company(03-14-2017)", "Title": "Operation Electric Powder - Who is targeting Israel Electric Company?", "Source": "<PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "94d25eef5fa8292df1b03a712789d1b0a6a8b39c", "Date": "03/14/2017", "Year": "2017"}, {"Filename": "FireEye_APT29-Domain-Fronting-With-TOR(03-27-2017)", "Title": "APT29 Domain Fronting With TOR", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "30438e854db6bb43fb6629c746fb01ccfa8673b8", "Date": "03/27/2017", "Year": "2017"}, {"Filename": "PaloAlto_Dimnie-Hiding-Plain-Sight(03-28-2017)", "Title": "Dimnie: Hiding in Plain Sight", "Source": "Palo Alto Networks", "Link": "https://app.box.com/s/********************************", "SHA-1": "cbd4f22d9402177c3cbe6f0c30c9e1e876e9c21d", "Date": "03/28/2017", "Year": "2017"}, {"Filename": "ESET_Carbon-Paper-Peering-into-Turlas-second-stage-backdoor(03-30-2017)", "Title": "Carbon Paper: Peering into Turla second stage backdoor", "Source": "ESET", "Link": "https://app.box.com/s/********************************", "SHA-1": "d06b05c9f20c34154b3134a02f9bdfcac5b570e9", "Date": "03/30/2017", "Year": "2017"}, {"Filename": "PWC_cloud-hopper-report-final-v4(04-03-2017)", "Title": "Operation Cloud Hopper", "Source": "PWC", "Link": "https://app.box.com/s/********************************", "SHA-1": "4082ecd0d6b73061fc12d099d0b1b257b3d6a71d", "Date": "04/03/2017", "Year": "2017"}, {"Filename": "<PERSON><PERSON><PERSON>_Lazarus-Under-The-Hood-PDF_final(04-03-2017)", "Title": "<PERSON> Under The Hood", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "9a6098c60b1f484550326ece77eb9339ddf2e6fe", "Date": "04/03/2017", "Year": "2017"}, {"Filename": "PaloAlto_The-Blockbuster-<PERSON><PERSON>(04-07-2017)", "Title": "The Blockbuster Sequel", "Source": "Palo Alto Networks", "Link": "https://app.box.com/s/********************************", "SHA-1": "c3bc5c67ac00f64fa55a9e6574c1b2131807848a", "Date": "04/07/2017", "Year": "2017"}, {"Filename": "Symantec_Longhorn-<PERSON><PERSON>(04-10-2017)", "Title": "Longhorn: Tools used by cyberespionage group", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "2854f4326f7784a76a6bccec982f76334fea02dd", "Date": "04/10/2017", "Year": "2017"}, {"Filename": "Proofpoint_APT-Targets-Financial-Analysts-CVE-2017-0199(04-27-2017)", "Title": "APT Targets Financial Analysts with CVE-2017-0199", "Source": "Proofpoint", "Link": "https://app.box.com/s/********************************", "SHA-1": "10a5f550f9265c745b75db913753bbed6c9a0413", "Date": "04/27/2017", "Year": "2017"}, {"Filename": "Cysinfo_Attack-Impersonating-Indian-Think-Tank-Target-CBIpdf(05-11-2017)", "Title": "Cyber Attack Impersonating Identity Of Indian Think Tank To Target Central Bureau Of Investigation (cbi) And Possibly Indian Army Officials", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "d99516fc275a38c063de39c0188a9dc829491e11", "Date": "05/11/2017", "Year": "2017"}, {"Filename": "FireEye_Cyber-Espionage-Alive-Well-APT32(05-14-2017)", "Title": "Cyber Espionage is Alive and Well: APT32 and the Threat to Global Corporations", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "1c8b853a0f1d42979c2d231a728cfb2173c5e991", "Date": "05/14/2017", "Year": "2017"}, {"Filename": "RecordedFuture_Chinese-Ministry-State-APT3(05-17-2017)", "Title": "Recorded Future Research Concludes Chinese Ministry of State Security Behind APT3", "Source": "Recorded Future", "Link": "https://app.box.com/s/********************************", "SHA-1": "d3549e1f2aad3cadcae13a201f5291cc3d2eaec6", "Date": "05/17/2017", "Year": "2017"}, {"Filename": "Cybereason_Large-Scale-APT-Asia(05-24-2017)", "Title": "Operation Cobalt Kitty: A large-scale APT in Asia carried out by the OceanLotus Group", "Source": "Cybereason", "Link": "https://app.box.com/s/********************************", "SHA-1": "bce3c61603239578e7a1f06fef1c30903ff0d391", "Date": "05/24/2017", "Year": "2017"}, {"Filename": "Cybereason_Cobalt-Kitty-ActorsProfiles-IOCs(05-24-2017)", "Title": "Operation Cobalt Kitty Threat Actor Profile & IOC", "Source": "Cybereason", "Link": "https://app.box.com/s/********************************", "SHA-1": "62bad1275ebdb062b9df708c1b71c34e5b92f310", "Date": "05/24/2017", "Year": "2017"}, {"Filename": "Citizenlab_Tainted-Leaks-Disinformation-Phishing-With-Russian-Nexus(05-25-2017)", "Title": "TAINTED LEAKS Disinformation and Phishing With a Russian Nexus", "Source": "Citizen Lab", "Link": "https://app.box.com/s/********************************", "SHA-1": "1721fa3665b069612d0f360fd1dcff628c0a26b1", "Date": "05/25/2017", "Year": "2017"}, {"Filename": "FireEye_Privileges-Credentials-Phished-Request-of-Counsel(06-06-2017)", "Title": "Privileges and Credentials: Phished at the Request of Counsel", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "6bc4c4b9d535e6579029edd185a930156ef4d353", "Date": "06/06/2017", "Year": "2017"}, {"Filename": "Microsoft_PLATINUM-evolve-find-ways-to-maintain-invisibility(06-07-2017)", "Title": "PLATINUM continues to evolve, find ways to maintain invisibility", "Source": "Microsoft", "Link": "https://app.box.com/s/********************************", "SHA-1": "c8f8d4cea73e6941d5b6bfe0caffbdd26d0ace2d", "Date": "06/07/2017", "Year": "2017"}, {"Filename": "ESET_Win32_Industroyer(06-12-2017)", "Title": "WIN32/INDUSTROYER A new threat for industrial control systems", "Source": "ESET", "Link": "https://app.box.com/s/********************************", "SHA-1": "9e6f9b8b5b22c32c1ec5d53ed1992f8f83013140", "Date": "06/12/2017", "Year": "2017"}, {"Filename": "Dragos_CrashOverride-01(06-12-2017)", "Title": "CRASHOVERRIDE Analysis of the Threat to Electric Grid Operations", "Source": "Dragos", "Link": "https://app.box.com/s/********************************", "SHA-1": "f86cc703b475135372a6639e12d4651121e23fff", "Date": "06/12/2017", "Year": "2017"}, {"Filename": "ThreatConnect_KASPERAGENT-Campaign-resurfaces-May-Election(06-14-2017)", "Title": "KASPERAGENT Malware Campaign resurfaces in May Election", "Source": "ThreatConnect", "Link": "https://app.box.com/s/********************************", "SHA-1": "cd8ae99c908220d52b892089c5d18ed39b0d0937", "Date": "06/14/2017", "Year": "2017"}, {"Filename": "RecordedFuture_North-Korea-Is-Not-Crazy(06-15-2017)", "Title": "North Korea Is Not Crazy", "Source": "Recorded Future", "Link": "https://app.box.com/s/********************************", "SHA-1": "197a1f8aaefbf84a993a8f02372ddcf6e9ac5a01", "Date": "06/15/2017", "Year": "2017"}, {"Filename": "Secureworks_Bronze-Butler-Report(06-23-2017)", "Title": "Bronze Butler", "Source": "Dell Secureworks", "Link": "https://app.box.com/s/********************************", "SHA-1": "e8e651ded2d19cb62556e14a2e79eec1e897c55d", "Date": "06/23/2017", "Year": "2017"}, {"Filename": "Kaspersky-From-BlackEnergy-to-ExPetr(06-30-2017)", "Title": "From BlackEnergy to ExPetr", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "fd25688495cc576eabf45affd51a9f2a2b85399b", "Date": "06/30/2017", "Year": "2017"}, {"Filename": "ESET_TeleBots-Supply-chain-attacks-against-Ukraine(06-30-2017)", "Title": "TeleBots are back: supply-chain attacks against Ukraine", "Source": "ESET", "Link": "https://app.box.com/s/********************************", "SHA-1": "8f2ab51999dfbd55e7bf6f15862b1a89ce6ebf72", "Date": "06/30/2017", "Year": "2017"}, {"Filename": "Citizenlab_Campaign-Chinese-language-news-sites(07-05-2017)", "Title": "An intrusion campaign targeting Chinese language news sites", "Source": "Citizen Lab", "Link": "https://app.box.com/s/********************************", "SHA-1": "e7a8a3e11bb427a1ca3e9c7f63decbc3e9310e47", "Date": "07/05/2017", "Year": "2017"}, {"Filename": "Bitdefender_Whitepaper-Inexsmar-A4-en-EN(07-18-2017)", "Title": "Inexsmar: An unusual DarkHotel campaig", "Source": "Bitdefender", "Link": "https://app.box.com/s/********************************", "SHA-1": "39e53915de468512258066c3ae2875770bd68c45", "Date": "07/18/2017", "Year": "2017"}, {"Filename": "Sentryo_EBOOK_CYBERATTACKS-AGAINST-UKRAINIAN-ICS(07-18-2017)", "Title": "Cyberattacks Against Ukrainian ICS", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "821e2036f0c3d06c508ad8c3442ef789d86d5bd6", "Date": "07/18/2017", "Year": "2017"}, {"Filename": "<PERSON><PERSON>_Operation_Wilted_Tulip(07-25-2017)", "Title": "Operation Wilted <PERSON><PERSON>", "Source": "<PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "71cb0af91eb32f444cd97f40934b0337e03fd379", "Date": "07/25/2017", "Year": "2017"}, {"Filename": "TrendMicro_ChessMaster-Makes-its-Move(07-27-2017)", "Title": "ChessMaster Makes its Move: A Look into the Campaign's Cyberespionage Arsenal", "Source": "Trend Micro", "Link": "https://app.box.com/s/********************************", "SHA-1": "01c32ccd1e596dbf0437b318f5ed5554a14c252a", "Date": "07/27/2017", "Year": "2017"}, {"Filename": "RSA_Russian-Bank-Offices-Phishing-Wave(08-18-2017)", "Title": "Russian Bank Offices Hit with Broad Phishing Wave", "Source": "RSA", "Link": "https://app.box.com/s/********************************", "SHA-1": "c42879e3e3393f1cd6a716273d4eee1b023029bd", "Date": "08/18/2017", "Year": "2017"}, {"Filename": "Kaspersky_Introducing-<PERSON><PERSON>ear(08-30-2017)", "Title": "Introducing WhiteBear", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "3dc6e80fc5ef663d8c211416165bf7110c0888df", "Date": "08/30/2017", "Year": "2017"}, {"Filename": "ESET_Gazer(08-30-2017)", "Title": "Gazing at Gazer", "Source": "ESET", "Link": "https://app.box.com/s/********************************", "SHA-1": "2c5e1689900d6f92ec985be4f9f1df9f9b52de30", "Date": "08/30/2017", "Year": "2017"}, {"Filename": "Symantec_Dragonfly-Western-energy-sector-targeted(09-06-2017)", "Title": "Dragonfly: Western energy sector targeted by sophisticated attack group", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "c7d082f04cb0d613c04517d0d6dec2d76a866c6b", "Date": "09/06/2017", "Year": "2017"}, {"Filename": "FireEye_CVE-2017-8759-Used-Wild-Distribute-FINSPY(09-12-2017)", "Title": "CVE-2017-8759: Zero-Day Used in the Wild to Distribute FINSPY", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "290a67462a2eaf75bef1a154709616e5e8accd29", "Date": "09/12/2017", "Year": "2017"}, {"Filename": "Intezer_Evidence-Aurora-Supply-Chain-Attack-CCleaner-Part1(09-20-2017)", "Title": "Evidence Aurora Operation Still Active: Supply Chain Attack Through CCleaner", "Source": "Intezer", "Link": "https://app.box.com/s/********************************", "SHA-1": "31bdd3f34c5583dc156b99986f5695368d103daa", "Date": "09/20/2017", "Year": "2017"}, {"Filename": "PaloAlto_Threat-Actors-Target-Government-Belarus-Using-CMSTAR-Trojan(09-28-2017)", "Title": "Threat Actors Target Government of Belarus Using CMSTAR Trojan", "Source": "Palo Alto Networks", "Link": "https://app.box.com/s/********************************", "SHA-1": "2cca07b8341b01d6a1eae3e10c8644532cfce14f", "Date": "09/28/2017", "Year": "2017"}, {"Filename": "Intezer_Evidence-Aurora-Supply-Chain-Attack-CCleaner-Part2(10-02-2017)", "Title": "Evidence Aurora Operation Still Active: Supply Chain Attack Through CCleaner part2", "Source": "Intezer", "Link": "https://app.box.com/s/********************************", "SHA-1": "0b53ce14766ad66118edce7cbe62ff2926fbd281", "Date": "10/02/2017", "Year": "2017"}, {"Filename": "BAESytems_Taiwan-He<PERSON>-<PERSON>-Tools-Ransomware(10-16-2017)", "Title": "Taiwan Heist: <PERSON> Ra<PERSON>", "Source": "BAE Systems", "Link": "https://app.box.com/s/********************************", "SHA-1": "0d9aab08a0ef223d0fba363b8c2ed4d0093ee291", "Date": "10/16/2017", "Year": "2017"}, {"Filename": "Kaspersky_BlackOasis-APT-zero-day(10-16-2017)", "Title": "BlackOasis APT and new targeted attacks leveraging zero-day exploit", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "18f110cc5441d442f5aa14b9b456fce56e59d8f4", "Date": "10/16/2017", "Year": "2017"}, {"Filename": "CiscoTalos_Cyber-Conflict-Decoy-Document-Used-In-Real-Cyber-Conflict(10-22-2017)", "Title": "Cyber Conflict Decoy Document Used In Real Cyber Conflict", "Source": "Cisco", "Link": "https://app.box.com/s/********************************", "SHA-1": "ff6b2725045833863974787939889f35cf71ca02", "Date": "10/22/2017", "Year": "2017"}, {"Filename": "Clearskysec_IranianThreatAgent-Greenbug(10-24-2017)", "Title": "Iranian Threat Agent <PERSON><PERSON> Impersonates Israeli High-Tech and Cyber Security Companies", "Source": "<PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "34fd37ba9b0b6181f0a7342cb1204c55a9c2137b", "Date": "10/24/2017", "Year": "2017"}, {"Filename": "RiskIQ_htpRAT-Malware-Attacks(10-26-2017)", "Title": "Remote Control Interloper: Analyzing New Chinese htpRAT Attacks Against ASEAN", "Source": "RiskIQ", "Link": "https://app.box.com/s/********************************", "SHA-1": "83bc7df01a0a0a638d5bae228c1fcc0c34884ca4", "Date": "10/26/2017", "Year": "2017"}, {"Filename": "PaloAlto_Tracking-<PERSON><PERSON><PERSON>-<PERSON><PERSON>-Leads-Threat-Actors-Repository(10-27-2017)", "Title": "Tracking Subaat: Targeted Phishing Attack Leads to Threat Actor's Repository", "Source": "Palo Alto Networks", "Link": "https://app.box.com/s/********************************", "SHA-1": "ca676e9c6c05be3a8fafdd40bf0b861a11bbb875", "Date": "10/27/2017", "Year": "2017"}, {"Filename": "NAOUK_Investigation-WannaCry-cyber-attack-and-the-NHS(10-27-2017)", "Title": "Investigation: WannaCry cyber attack and the NHS ", "Source": "NAO UK", "Link": "https://app.box.com/s/********************************", "SHA-1": "db1db6be0d6564d5bff88843845c53d3540017aa", "Date": "10/27/2017", "Year": "2017"}, {"Filename": "PWC_KeyBoys-are-back-in-town(11-02-2017)", "Title": "The KeyBoys are back in town", "Source": "PWC", "Link": "https://app.box.com/s/********************************", "SHA-1": "02a369792c2bb0ac187236215efcf16f8c033c53", "Date": "11/02/2017", "Year": "2017"}, {"Filename": "TrendMicro_ChessMasters-New-Strategy-Evolving-Tools-Tactics(11-06-2017)", "Title": "ChessMaster's New Strategy: Evolving Tools and Tactics", "Source": "Trend Micro", "Link": "https://app.box.com/s/********************************", "SHA-1": "83092276fe4d5c3c656d5811413f4560e3653a41", "Date": "11/06/2017", "Year": "2017"}, {"Filename": "Volexity_OceanLotus-Mass-Digital-Surveillance-Targeting-ASEAN-Media(11-06-2017)", "Title": "OceanLotus Blossoms: Mass Digital Surveillance and Attacks Targeting ASEAN", "Source": "Volexity", "Link": "https://app.box.com/s/********************************", "SHA-1": "43b8a4716abedd6f8b2c9e2cda5b038407a0cfb4", "Date": "11/06/2017", "Year": "2017"}, {"Filename": "McAfee_APT28-Office-Malware-Doc-Citing-NYC-TerrorAttack(11-07-2017)", "Title": "Threat Group APT28 Slips Office Malware into Doc Citing NYC Terror Attack", "Source": "McAfee", "Link": "https://app.box.com/s/********************************", "SHA-1": "ce610914fa4b49e4ba6fb04d36d88a488eddc774", "Date": "11/07/2017", "Year": "2017"}, {"Filename": "PaloAlto_OilRig-Deploys-ALMA-DNS-Tunneling-Trojan(11-08-2017)", "Title": "OilRig Deploys \"ALMA Communicator\" - DNS Tunneling Trojan", "Source": "Palo Alto Networks", "Link": "https://app.box.com/s/********************************", "SHA-1": "6df36ad15520973c3af5e17748c7b1a2f72f7011", "Date": "11/08/2017", "Year": "2017"}, {"Filename": "RSA_the-carbanak-fin7-syndicate(11-22-2017)", "Title": "The Carbanak/Fin7 syndicate", "Source": "RSA", "Link": "https://app.box.com/s/********************************", "SHA-1": "54fb362a0c807a40c4d0a0bc35bde59ed84f0f98", "Date": "11/22/2017", "Year": "2017"}, {"Filename": "NCSC_Turla-Neuron-Nautilus-Snake-malware_1(11-22-2017)", "Title": "Turla group using Neuron and Nautilus tools alongside Snake malware", "Source": "NCSC", "Link": "https://app.box.com/s/********************************", "SHA-1": "316cd38c3939a4f862b611888a489672c014b651", "Date": "11/22/2017", "Year": "2017"}, {"Filename": "RSA_the-shadows-of-ghosts-carbanak-report(11-30-2017)", "Title": "Inside the Response of a Unique CARBANAK Intrusion", "Source": "RSA", "Link": "https://app.box.com/s/********************************", "SHA-1": "06c63b54de2848bff9557bc9c49bf75906273fb2", "Date": "11/30/2017", "Year": "2017"}, {"Filename": "<PERSON><PERSON>_<PERSON><PERSON>_Kitten_2017(12-05-2017)", "Title": "<PERSON><PERSON>: Iranian Cyber Espionage Against Human Rights Activists", "Source": "<PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "bba8af0fe4254863c86aafeedb7d86f7af7b0e99", "Date": "12/05/2017", "Year": "2017"}, {"Filename": "Clearsky_Charming-Kitten-2017(12-05-2017).csv", "Title": "<PERSON><PERSON>: CSV Data", "Source": "<PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "e55cf58e470f10affdb7dd0664868d014a2c8d13", "Date": "12/05/2017", "Year": "2017"}, {"Filename": "Dragos_TRISIS-01(12-14-2017)", "Title": "TRISIS Malware", "Source": "Dragos", "Link": "https://app.box.com/s/********************************", "SHA-1": "c9534efcbf1a9c020ba864ee090e0c0b03d32181", "Date": "12/14/2017", "Year": "2017"}, {"Filename": "Fireeye_ICS-Attack-Framework-TRITON(12-14-2017)", "Title": "Attackers Deploy New ICS Attack Framework \"TRITON\" and Cause Operational Disruption to Critical Infrastructure", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "b0d8df40d920ddbd5372adef63486c23545a141c", "Date": "12/14/2017", "Year": "2017"}, {"Filename": "ProofPoint_us-wp-north-korea-bitten-by-bitcoin-bug(12-19-2017)", "Title": "North Korea Bitten by Bitcoin Bug", "Source": "Proofpoint", "Link": "https://app.box.com/s/********************************", "SHA-1": "d8ba9a371c7dc7e6608b92e8a671b82191d6c67a", "Date": "12/19/2017", "Year": "2017"}, {"Filename": "TrendMicro_Update-PawnStorm-Politically-Motivated-Campaigns(01-12-2018)", "Title": "Update on Pawn Storm: New Targets and Politically Motivated Campaigns", "Source": "Trend Micro", "Link": "https://app.box.com/s/********************************", "SHA-1": "fa6a1828636af0c6bc1feab3748e741b9123c174", "Date": "01/12/2018", "Year": "2018"}, {"Filename": "Lookout_Dark-Caracal_srr_20180118_us_v.1.0(01-18-2018)", "Title": "Dark Caracal Cyber-espionage at a Global Scale", "Source": "Lookout", "Link": "https://app.box.com/s/********************************", "SHA-1": "d5c605fd42adb5312d17a54d246d8178a6a9094c", "Date": "01/18/2018", "Year": "2018"}, {"Filename": "NCSC_Turla-Neuron-Malware-Update(01-18-2018)", "Title": "Turla group update Neuron malware", "Source": "NCSC", "Link": "https://app.box.com/s/********************************", "SHA-1": "a91289e835991b389e01254492d0fe84aeb21752", "Date": "01/18/2018", "Year": "2018"}, {"Filename": "Fireeye_rpt_APT37(02-20-2018)", "Title": "APT37 (Reaper): The Overlooked North Korean Actor", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "8742755080062ec87eb6ae0059fd7925cde83a9b", "Date": "02/20/2018", "Year": "2018"}, {"Filename": "Dragos_2017-Review-Industrial-Control-System-Threats(03-01-2018)", "Title": "Industrial Control System Threats", "Source": "Dragos", "Link": "https://app.box.com/s/********************************", "SHA-1": "c7ab93fca304e61cca180f7e2e43f2071a85c749", "Date": "03/01/2018", "Year": "2018"}, {"Filename": "McAfee_Hidden-Cobra-Turkish-Financial-Sector-Bankshot-Implant(03-08-2018)", "Title": "Hidden Cobra Targets Turkish Financial Sector With New Bankshot Implant", "Source": "McAfee", "Link": "https://app.box.com/s/********************************", "SHA-1": "f6ebca71a6ed6d77b662d8f855d6db90194102d1", "Date": "03/08/2018", "Year": "2018"}, {"Filename": "NCCGroup_APT15-alive-analysis-RoyalCli-RoyalDNS(03-10-2018)", "Title": "APT15 is alive and strong: An analysis of RoyalCli and RoyalDNS", "Source": "NCC Group", "Link": "https://app.box.com/s/********************************", "SHA-1": "3de01836586fa9b94c0ab55e84ad715b2af4aa3c", "Date": "03/10/2018", "Year": "2018"}, {"Filename": "Intezer_Lazarus-Cryptocurrency-Exchanges-FinTech-Companies(03-28-2018)", "Title": "Lazarus Group Targets More Cryptocurrency Exchanges and FinTech Companies", "Source": "Intezer", "Link": "https://app.box.com/s/********************************", "SHA-1": "97c1a7625602d1dc66a153016ac63631f9b65d82", "Date": "03/28/2018", "Year": "2018"}, {"Filename": "Fireeye_mtrends-2018(04-05-2018)", "Title": "M-TRENDS2018", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "4f6055c44e39ff39898e5d4f3f05bf6fad74af85", "Date": "04/05/2018", "Year": "2018"}, {"Filename": "Fireeye_rpt-fin6(04-20-2018)", "Title": "Follow The Money: Dissecting the Operations of the Cyber Crime Group FIN", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "86d217e501fdd470311a9f623ca825f50c1d90b6", "Date": "04/20/2018", "Year": "2018"}, {"Filename": "<PERSON><PERSON>sky_EB_public_FINAL_EN_20042018(04-23-2018)", "Title": "Energetic Bear/Crouching Yeti: attacks on servers", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "70cd520c9b5860acb9e27cb19ad0b1270687d688", "Date": "04/23/2018", "Year": "2018"}, {"Filename": "401TRG_Burning_Umbrella(05-04-2018)", "Title": "Burning Umbrella", "Source": "401TRG", "Link": "https://app.box.com/s/********************************", "SHA-1": "65dbdb2c06922707a0979295c9645c71cf0d2963", "Date": "05/04/2018", "Year": "2018"}, {"Filename": "RecordedFuture_cta-2018-0509(05-09-2018)", "Title": "Iran's Hacker Hierarchy Exposed", "Source": "Recorded Future", "Link": "https://app.box.com/s/********************************", "SHA-1": "a79482c2974edab7268fe843e75b834fb073dbaa", "Date": "05/09/2018", "Year": "2018"}, {"Filename": "Intrusiontruth_The-destruction-ofAPT3(05-22-2018)", "Title": "The destruction of APT3", "Source": "Intrusiontruth", "Link": "https://app.box.com/s/********************************", "SHA-1": "6fa8f171b0c54e32f84947822529ec3577304030", "Date": "05/22/2018", "Year": "2018"}, {"Filename": "Ka<PERSON><PERSON>_LuckyMouse-datacenter-waterholing-campaign(06-13-2018)", "Title": "LuckyMouse hits national data center to organize country-level waterholing campaign", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "6c2a04c303f73b5babacb999ff10008584eaf254", "Date": "06/13/2018", "Year": "2018"}, {"Filename": "<PERSON><PERSON><PERSON>_OlympicDestroyerisstillalive(06-19-2018)", "Title": "Olympic Destroyer is still alive", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "267c3ec477cc853b163bb41a8fd82cdf6c51f4db", "Date": "06/19/2018", "Year": "2018"}, {"Filename": "Kaspersky_APT-Trends-Report-Q2-2018(07-10-2018)", "Title": "APT Trends Report Q2 2018", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "0ce2a58c7b5cfd8b5299ecbe9321e4124c1b4066", "Date": "07/10/2018", "Year": "2018"}, {"Filename": "Fortinet_Russian-<PERSON>oy-Leads-BISKVIT(08-20-2018)", "Title": "Russian Army Exhibition Decoy Leads to New BISKVIT Malware", "Source": "Fortinet", "Link": "https://app.box.com/s/********************************", "SHA-1": "e3fc342ccb48ec97aeec128210e04da127d5a94f", "Date": "08/20/2018", "Year": "2018"}, {"Filename": "ESTSecurity_ESRC-1808-TLP-White-IR002_RocketMan_English(08-22-2018)", "Title": "OPERATION \"Rocket Man\"", "Source": "ESTSecurity", "Link": "https://app.box.com/s/********************************", "SHA-1": "bc542b523ca87d2dad6195cb0f41885ff08e4507", "Date": "08/22/2018", "Year": "2018"}, {"Filename": "Crowdstrike_Two-Birds-One-STONEPANDA(08-30-2018)", "Title": "Two Birds, One STONE PANDA", "Source": "Crowdstrike", "Link": "https://app.box.com/s/********************************", "SHA-1": "6ae6923748844d1fbb0a08d07d6bcb1112122796", "Date": "08/30/2018", "Year": "2018"}, {"Filename": "Checkpoint_Domestic-Kitten-Iranian-Surveillance-Operation(09-07-2018)", "Title": "Domestic Kitten: An Iranian Surveillance Operation", "Source": "Checkpoint", "Link": "https://app.box.com/s/********************************", "SHA-1": "60ffe642ed8d9d2171b4c44729c4687fe493e87d", "Date": "09/07/2018", "Year": "2018"}, {"Filename": "McAfee_rp-operation-oceansalt(10-17-2018)", "Title": "Operation Oceansalt Delivers Wave After Wave", "Source": "McAfee", "Link": "https://app.box.com/s/********************************", "SHA-1": "1bf64f3fe87c916e250e3c9058d7de553e1cbbd2", "Date": "10/17/2018", "Year": "2018"}, {"Filename": "Mcafee_OperationSharpshooter(12-12-2018)", "Title": "https://securingtomorrow.mcafee.com/blogs/other-blogs/mcafee-labs/operation-sharpshooter-targets-global-defense-critical-infrastructure/", "Source": "McAfee", "Link": "https://app.box.com/s/********************************", "SHA-1": "f9c7b343afa46a5051d17fbf8299f46e28319ee6", "Date": "12/12/2018", "Year": "2018"}, {"Filename": "PaloAlto_Shamoon3-Targets-Oil-and-Gas(12-13-2018)", "Title": "Shamoon 3 Targets Oil and Gas Organization", "Source": "Palo Alto Networks", "Link": "https://app.box.com/s/********************************", "SHA-1": "956aca96f9534169cc8e43fefdbc869d48b75676", "Date": "12/13/2018", "Year": "2018"}, {"Filename": "<PERSON><PERSON><PERSON>_Chafer-Remexi-Iran-diplomatic(01-30-2019)", "Title": "<PERSON><PERSON> used Remexi malware to spy on Iran-based foreign diplomatic entities", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "b4972c4bc6cc763054e9cfd16970303c7a0f6c35", "Date": "01/30/2019", "Year": "2019"}, {"Filename": "SecureSoft_Lazarus_Russia(02-20-2019)", "Title": "Attacks Of The Lazarus Cybercriminal Group Attended To Organizations In Russia", "Source": "SecureSoft", "Link": "https://app.box.com/s/********************************", "SHA-1": "d110dd857cf8a4cc6d9d0f529e799e96fcf6121f", "Date": "02/20/2019", "Year": "2019"}, {"Filename": "DellSecureworks_Peek-BRONZE-UNION-Toolbox(02-27-2019)", "Title": "A Peek into BRONZE UNION's Toolbox", "Source": "Dell Secureworks", "Link": "https://app.box.com/s/********************************", "SHA-1": "1486155a4fdba37407cb86965e3a5e894a7fe0a1", "Date": "02/27/2019", "Year": "2019"}, {"Filename": "Qihoo360_PatBear-APT-C-37-Armed-Organizations-Attacks(03-25-2019)", "Title": "<PERSON> (APT-C-37)", "Source": "Qihoo 360", "Link": "https://app.box.com/s/********************************", "SHA-1": "eefffff049213f895e81e03bcd27d0d7be2c8b1e", "Date": "03/25/2019", "Year": "2019"}, {"Filename": "Cylance_OceanLotus-Steganography-Malware-Analysis-White-Paper(04-02-2019)", "Title": "OceanLotus APT Group Leveraging Steganography", "Source": "Cylance", "Link": "https://app.box.com/s/********************************", "SHA-1": "6645296c925133446d4e213a547235692761d5c2", "Date": "04/02/2019", "Year": "2019"}, {"Filename": "CiscoTalos_SeaTurtle-DNS-hijacking-April(04-17-2019)", "Title": "DNS Hijacking Abuses Trust In Core Internet Service", "Source": "Cisco", "Link": "https://app.box.com/s/********************************", "SHA-1": "936b76720ffc221066235838b40ef9e7548c58ab", "Date": "04/17/2019", "Year": "2019"}, {"Filename": "Fireeye_CARBANAK-Week-1-Rare-Occurrence(04-22-2019)", "Title": "CARBANAK Week Part One: A Rare Occurrence", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "d0506df85bd5faf87a4a67e6affcde4ce9dda48f", "Date": "04/22/2019", "Year": "2019"}, {"Filename": "Fireeye_CARBANAK-Week-2-CARBANAK-SourceCode-Analysis(04-23-2019)", "Title": "CARBANAK Week Part Two: Continuing the CARBANAK Source Code Analysis", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "9468ff9d1bc06b4c9f187e4ab55ae485750d6c5b", "Date": "04/23/2019", "Year": "2019"}, {"Filename": "Fireeye_CARBANAK-Week-3-Behind-CARBANAK-Backdoor(04-24-2019)", "Title": "CARBANAK Week Part Three: Behind the CARBANAK Backdoor", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "98775eb539a512859a786f6292cc6f6db04916b2", "Date": "04/24/2019", "Year": "2019"}, {"Filename": "Fireeye_CARBANAK-Week-4-CARBANAK-Desktop-Video-Player(04-25-2019)", "Title": "CARBANAK Week Part Four: The CARBANAK Desktop Video Player", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "7de0fbb852cc5be3fce91725a1b0aeb9abe3a43b", "Date": "04/25/2019", "Year": "2019"}, {"Filename": "ThaiCERT-A_Threat_Actor_Encyclopedia(06-19-2019)", "Title": "Threat Group Cards: A Threat Actor Encyclopedia", "Source": "ThaiCERT", "Link": "https://app.box.com/s/********************************", "SHA-1": "bb2713205e66ebc71b31c97af9406ec6387fdb8f", "Date": "06/19/2019", "Year": "2019"}, {"Filename": "Symantec_Waterbug-Group-NewToolset(06-20-2019)", "Title": "Waterbug: Espionage Group Rolls Out Brand-New Toolset in Attacks Against Governments", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "9d996d92194bf386a8e7555d618eb5cf183e379a", "Date": "06/20/2019", "Year": "2019"}, {"Filename": "Cybereason_Operation-SoftCell-Telecom(06-25-2019)", "Title": "Operation Soft Cell: A Worldwide Campaign Against Telecommunications Providers", "Source": "Cybereason", "Link": "https://app.box.com/s/********************************", "SHA-1": "31cb2f45b6a0cf0234088a2c05a9e69bfe444d4b", "Date": "06/25/2019", "Year": "2019"}, {"Filename": "CiscoTalos_SeaTurtle-DNS-hijacking(07-09-2019)", "Title": "<PERSON> Turtle keeps on swimming, finds new victims, DNS hijacking techniques", "Source": "Cisco", "Link": "https://app.box.com/s/********************************", "SHA-1": "bb8c51c9f8f64d656a65c3f7c3b34f2135f8264c", "Date": "07/09/2019", "Year": "2019"}, {"Filename": "Fireeye_APT34-<PERSON><PERSON><PERSON>-<PERSON><PERSON>-Professional-Network(07-18-2019)", "Title": "Hard Pass: Declining APT34's <PERSON><PERSON>te to Join Their Professional Network", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "f937f0fe43db587ba38a755f06fa76bcd75d47c7", "Date": "07/18/2019", "Year": "2019"}, {"Filename": "Fireeye_rpt-apt41(08-07-2019)", "Title": "APT41: A Dual Espionage and Cyber Crime Operation", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "30de1a1401504cd9f028862b821b98348b53e676", "Date": "08/07/2019", "Year": "2019"}, {"Filename": "Fireeye_APT41-Dual-Espionage-Cyber-Crime-WebPage(08-07-2019)", "Title": "APT41: A Dual Espionage and Cyber Crime Operation", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "251adf184f43ba9101a4f3df9ff87da065eb58b3", "Date": "08/07/2019", "Year": "2019"}, {"Filename": "Kaspersky_Recent-Cloud-Atlas-activity(08-12-2019)", "Title": "Recent Cloud Atlas activity", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "474e2d3e3f4a8c45f73a2c34ff568c3c7d51e400", "Date": "08/12/2019", "Year": "2019"}, {"Filename": "Fireeye_GAMEOVER-Detecting-Stopping-APT41-Operation(08-19-2019)", "Title": "GAME OVER: Detecting and Stopping an APT41 Operation", "Source": "FireEye", "Link": "https://app.box.com/s/********************************", "SHA-1": "8093d518676b9c7a41b83927198e16c08b187dbb", "Date": "08/19/2019", "Year": "2019"}, {"Filename": "Symantec_Tortoiseshell-Targets-ITProviders-Saudi-Supply-Chain(09-18-2019)", "Title": "Tortoiseshell Group Targets IT Providers in Saudi Arabia in Probable Supply Chain Attacks", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "b5072f526ef09287443c6a4b437a2219fb62c5d1", "Date": "09/18/2019", "Year": "2019"}, {"Filename": "Contextis_AVIVORE-Aerospace-Supply-Chain(10-03-2019)", "Title": "AVIVORE - Hunting Global Aerospace through the Supply Chain", "Source": "Contextis", "Link": "https://app.box.com/s/********************************", "SHA-1": "f0fbe7cbd9b7a3d77550a48e6ac853593ca182bb", "Date": "10/03/2019", "Year": "2019"}, {"Filename": "<PERSON>sky_The-Kittens-Are-Back-in-Town-2-1(10-07-2019)", "Title": "The Kittens Are Back in Town 2", "Source": "<PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "ed60cfb74a60a22fba34b7d85fec2e4b5ca72024", "Date": "10/07/2019", "Year": "2019"}, {"Filename": "Crowdstrike_huge-fan-of-your-work-intelligence-report(10-14-2019)", "Title": "Huge Fan of Your Work: TURBINE PANDA C919 Passenger Jet", "Source": "Crowdstrike", "Link": "https://app.box.com/s/********************************", "SHA-1": "d30068cc3f5856ba52df3b019e5eaa8653e22d77", "Date": "10/14/2019", "Year": "2019"}, {"Filename": "ESET_Operation_Ghost_Dukes(10-19-2019)", "Title": "Operation Ghost", "Source": "ESET", "Link": "https://app.box.com/s/********************************", "SHA-1": "d6f66a51549b7c2090135e8cb8c40a68c152018d", "Date": "10/19/2019", "Year": "2019"}, {"Filename": "calypso-apt-2019-eng(10-31-2019)", "Title": "Calypso APT: new group attacking state institutions", "Source": "Positive Technologies", "Link": "https://app.box.com/s/********************************", "SHA-1": "fa36f2632e6b9ff400f8b3ad9539f3bf4a586dec", "Date": "10/31/2019", "Year": "2019"}, {"Filename": "Ka<PERSON>sky_DarkUniverse-APT-framework27(11-05-2019)", "Title": "DarkUniverse - the mysterious APT framework 27", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "4af537ca5ddf17db79dc32d4599e62434eaef1b3", "Date": "11/05/2019", "Year": "2019"}, {"Filename": "Deepinstinct_Untanglin-Legion(12-18-2019)", "Title": "Untangling Legion Loader's Hornet Nest of Malware", "Source": "Deepinstinct", "Link": "https://app.box.com/s/********************************", "SHA-1": "4b99bc3975386ba8e0daa9ddda4dd9efc8595b7e", "Date": "12/18/2019", "Year": "2019"}, {"Filename": "Symantec_SED_GEN_Report_IAP-WP100(01-24-2020)", "Title": "Current Iran-Associated Cyber Threats", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "05b243bd81b310656d3348ee6d508de2f12a50d4", "Date": "01/24/2020", "Year": "2020"}, {"Filename": "Fireeye_APT41-Global-Intrusion-Campaign(03-25-2020)", "Title": "This Is Not a Test: APT41 Initiates Global Intrusion Campaign Using Multiple Exploits", "Source": "Fireeye", "Link": "https://app.box.com/s/********************************", "SHA-1": "5c1475cda2594e45e4f09bfc0c8b351fa70117b9", "Date": "03/25/2020", "Year": "2020"}, {"Filename": "Kaspersky_Transparent-Tribe-Evolution-analysis-part1(08-20-2020)", "Title": "Transparent Tribe: Evolution analysis, part 1", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "87ab4c2ff18e568da5932e17ea24c76d9a467938", "Date": "08/20/2020", "Year": "2020"}, {"Filename": "Kasperskty_Transparent-Tribe-Evolution-analysis-part-2(08-26-2020)", "Title": "Transparent Tribe: Evolution analysis, part 2", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "8c286ad8cb46ace5ab34ccdcf19269b92a0778a7", "Date": "08/26/2020", "Year": "2020"}, {"Filename": "ESET_Lazarus-supply-chain-attack-SouthKorea(11-16-2020)", "Title": "Lazarus supply-chain attack in South Korea", "Source": "ESET", "Link": "https://app.box.com/s/********************************", "SHA-1": "d775eada6c8549d916011b4505bc4862328c454b", "Date": "11/16/2020", "Year": "2020"}, {"Filename": "Vincss-RE018-ChinaPanda-attack-supply-chain-against-Vietnam-1(12-19-2020)", "Title": "China Panda attacks supply chain against Vietnam Government Certification Authority - Part1", "Source": "Vincss", "Link": "https://app.box.com/s/********************************", "SHA-1": "e22b4351d5cdae2ce8ec011d65b1b2753fa32b65", "Date": "12/19/2020", "Year": "2020"}, {"Filename": "Truesec_Collaboration-Between-FIN7-RYUK-Group(12-21-2020)", "Title": "Collaboration Between FIN7 and the RYUK Group", "Source": "Truesec", "Link": "https://app.box.com/s/********************************", "SHA-1": "532cc87c0ed43a88186014fba9fe3152bb785c3a", "Date": "12/21/2020", "Year": "2020"}, {"Filename": "<PERSON><PERSON><PERSON>_<PERSON>-covets-COVID-19-related-intelligence(12-23-2020)", "Title": "<PERSON> covets COVID-19-related intelligence", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "9fc555a3933061d78232c6bff01c52e10dd473fb", "Date": "12/23/2020", "Year": "2020"}, {"Filename": "Uptycs_RevengeRAT-Targeting-Users-SouthAmerica(12-29-2020)", "Title": "Revenge RAT Targeting Users in South America", "Source": "Uptycs", "Link": "https://app.box.com/s/********************************", "SHA-1": "9dc10a08df2833941a79f04ee5dd9d48e354cb8f", "Date": "12/29/2020", "Year": "2020"}, {"Filename": "RecordedFuture_pov-2020-1230(12-30-2020)", "Title": "SolarWinds Attribution: Are We Getting Ahead of Ourselves?", "Source": "RecordedFuture", "Link": "https://app.box.com/s/********************************", "SHA-1": "b555bc89a3a85a38751c79a996685e07d2b86767", "Date": "12/30/2020", "Year": "2020"}, {"Filename": "Microsoft_New nation-state cyberattacks(03-02-2021)", "Title": "New nation-state cyberattacks", "Source": "Microsoft", "Link": "https://app.box.com/s/********************************", "SHA-1": "9d1be61ac8a5f9fb884ea281009ca60c6bc9505c", "Date": "03/02/2021", "Year": "2021"}, {"Filename": "Cybereason_PortDoor -ChineseAPT-Targets-Russian-Defense-Sector(04-30-2021)", "Title": "PortDoor: New Chinese APT Backdoor Attack Targets Russian Defense Sector", "Source": "Cybereason", "Link": "https://app.box.com/s/********************************", "SHA-1": "c02efad2fd7ed95d44749a8f72d57ecb46f9b9af", "Date": "04/30/2021", "Year": "2021"}, {"Filename": "Talos_InSideCopy(07-07-2021)", "Title": " InSideCopy: How this APT continues to evolve its arsenal ", "Source": "<PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "2f44839afed1c12fd6e2e7f0fa8dd38dd632b065", "Date": "07/07/2021", "Year": "2021"}, {"Filename": "Kaspersky-LuminousMothAPT-Sweeping-attackspdf(07-14-2021)", "Title": "LuminousMoth APT: Sweeping attacks for the chosen few", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "a4d6f4b61271e4b5b7d4796d39ebae5b8b7e1728", "Date": "07/14/2021", "Year": "2021"}, {"Filename": "Volexity_NorthKorean-APT-InkySquid-Infects-Victims-Using-Browser-Exploits(08-17-2021)", "Title": "North Korean APT InkySquid Infects Victims Using Browser Exploits", "Source": "Volexity", "Link": "https://app.box.com/s/********************************", "SHA-1": "c807a583c68a2ca537c680e17ed6a20c9d3f0491", "Date": "08/17/2021", "Year": "2021"}, {"Filename": "Inquest_Kimsuky-Espionage-Campaign(08-23-2021)", "Title": "Kimsuky Espionage Campaign", "Source": "Inquest", "Link": "https://app.box.com/s/********************************", "SHA-1": "9327fb3746782368434192779e9c71bf63d20361", "Date": "08/23/2021", "Year": "2021"}, {"Filename": "TrendMicro_earth-baku-indo-pacific-countries(08-24-2021)", "Title": "APT41 Resurfaces as Earth Baku With New Cyberespionage Campaign", "Source": "Trend Micro", "Link": "https://app.box.com/s/********************************", "SHA-1": "f5ba5f8240569e08da4796deb95b89ee1340d03d", "Date": "08/24/2021", "Year": "2021"}, {"Filename": "Anomali_FIN7-Windows11-Themed-Drop-Javascript-Backdoor(09-02-2021)", "Title": "FIN7 Using Windows 11 Alpha-Themed Docs to Drop Javascript Backdoor", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "2d4ba0588b082e5b07f41bc5245dc57bd61c9921", "Date": "09/02/2021", "Year": "2021"}, {"Filename": "Cyble_APT-Targets-Indian-Defense-Enhanced-TTPs(09-14-2021)", "Title": "APT Group Targets Indian Defense Officials Through Enhanced TTPs", "Source": "Cyble", "Link": "https://app.box.com/s/********************************", "SHA-1": "56fb96e4d2cd53c6683763d1f81cc69c71e8f9c6", "Date": "09/14/2021", "Year": "2021"}, {"Filename": "Cyble_APT-Targets-Indian-Defense-Enhanced-TTPs(09-14-2021)", "Title": "APT Group Targets Indian Defense Officials Through Enhanced TTPs", "Source": "Cyble", "Link": "https://app.box.com/s/********************************", "SHA-1": "56fb96e4d2cd53c6683763d1f81cc69c71e8f9c6", "Date": "09/14/2021", "Year": "2021"}, {"Filename": "Talos-Operation<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(09-23-2021)", "Title": "Operation Armor Piercer: Targeted attacks in the Indian subcontinent using commercial RATs", "Source": "Cisco", "Link": "https://app.box.com/s/5zr8u8h2xy4tbllcxhashoq48cyetlfk", "SHA-1": "2dd9a2380a1145b88d521cc1cd413d7716958810", "Date": "09/23/2021", "Year": "2021"}, {"Filename": "PaloAlto-God<PERSON>-<PERSON><PERSON><PERSON>(11-07-2021)", "Title": "Targeted Attack Campaign Against ManageEngine ADSelfService Plus Delivers Godzilla Webshells, NGLite Trojan and KdcSponge Stealer", "Source": "PaloAlto", "Link": "https://app.box.com/s/********************************", "SHA-1": "5879f7a783531da3de5dc49323b224193d33a9bc", "Date": "11/07/2021", "Year": "2021"}, {"Filename": "Fortinet_Phishing-Targeting-Korean-Agent<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(12-10-2021)", "Title": "Phishing Campaign Targeting Korean to Deliver Agent <PERSON><PERSON>ariant", "Source": "Fortinet", "Link": "https://app.box.com/s/********************************", "SHA-1": "299c27eb159fe1a4bfbe577d7e483cee054d2d96", "Date": "12/10/2021", "Year": "2021"}, {"Filename": "AhnLab_Analysis-Report-of-Kimsuky-Group(01-05-2022)", "Title": "Kimsuky Group's APT Attacks (AppleSeed, PebbleDash)", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "3cef77aed2d0be3eeacd1f63d421ece2656f7a93", "Date": "01/05/2022", "Year": "2022"}, {"Filename": "CISA_AA22-011A_TLP-WHITE_01-10-22_v1(01-11-2022)", "Title": "Understanding and Mitigating Russian State-Sponsored Cyber Threats to U.S. Critical Infrastructure", "Source": "cisa", "Link": "https://app.box.com/s/********************************", "SHA-1": "61a5eb27ef3e633be66583c127de39fe19be7fb8", "Date": "01/11/2022", "Year": "2022"}, {"Filename": "Zscaler_Molerats-APT-targeting-Middle-East(01-20-2022)", "Title": "New espionage attack by Molerats APT targeting users in the Middle East", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "ccd8bb80544272f65b22974f8769c60a60e09ee6", "Date": "01/20/2022", "Year": "2022"}, {"Filename": "mp.weixin.qq.com_Falseflag-Sea-lotus-Glitch(01-20-2022)", "Title": "False flag or upgrade? Suspected sea lotus uses the Glitch platform to reproduce the attack sample", "Source": "Blog", "Link": "https://app.box.com/s/********************************", "SHA-1": "35428b46bb9785b339a3459fb35e184d711dbb99", "Date": "01/20/2022", "Year": "2022"}, {"Filename": "Trellix_PrimeMinisters-Details-Espionage-Campaign(01-25-2022)", "Title": "Prime Minister's Office Compromised: Details of Recent Espionage Campaign", "Source": "Trellix", "Link": "https://app.box.com/s/********************************", "SHA-1": "3593410f781e8eb7a90bf4416630e3d34f6eec0f", "Date": "01/25/2022", "Year": "2022"}, {"Filename": "bfv_cyber-brief-Nr1(01-26-2022)", "Title": "BfV Cyber-Brief Nr. 01/2022", "Source": "BfV", "Link": "https://app.box.com/s/********************************", "SHA-1": "c17307c0c37e409e7fc8a7b163b82670f3fce9b2", "Date": "01/26/2022", "Year": "2022"}, {"Filename": "MalwareBytes_NorthKoreas-Lazarus-APT-Windows-Update-GitHub(01-27-2022)", "Title": "North Korea's Lazarus APT leverages Windows Update client, GitHub in latest campaign", "Source": "MalwareBytes", "Link": "https://app.box.com/s/********************************", "SHA-1": "99973488f071190f68c62602b55da4c1e6f5c339", "Date": "01/27/2022", "Year": "2022"}, {"Filename": "Crowdstrike_StellarParticle-Campaign-Novel-Tactics-Techniques(01-27-2022)", "Title": "Observations from the StellarParticle Campaign", "Source": "crowdstrike", "Link": "https://app.box.com/s/********************************", "SHA-1": "a16e7cba15ff41a224278d6cb7037e65d8cf6e8b", "Date": "01/27/2022", "Year": "2022"}, {"Filename": "CyberGeeks_Lazarus-Malware-Notepad-Shell-Extension(01-31-2022)", "Title": "A detailed analysis of Lazarus APT malware disguised as Notepad++ Shell Extension", "Source": "Cyber Geeks", "Link": "https://app.box.com/s/********************************", "SHA-1": "c6ac7b0a3ee46801869cb818527c42054be86652", "Date": "01/31/2022", "Year": "2022"}, {"Filename": "Symantec_Shuckworm-Cyber-Attacks-Ukraine(01-31-2022)", "Title": "Shuckworm Continues Cyber-Espionage Attacks Against Ukraine", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "c345da781e48105661251b94261c83e8d52058d2", "Date": "01/31/2022", "Year": "2022"}, {"Filename": "cert.gov.ua_CERT-UA-3799(02-02-2022)", "Title": "Cyber attack of UAC-0056 group on state organizations of Ukraine using malicious programs SaintBot and OutSteel (CERT-UA #3799)", "Source": "CERT-UA", "Link": "https://app.box.com/s/********************************", "SHA-1": "f2d9a3ed66dbeb41b5887dab7308059f8e3e6fc4", "Date": "02/02/2022", "Year": "2022"}, {"Filename": "Symantec_Antlion-ChineseAPT-Target-Financial-Taiwan(02-03-2022)", "Title": "Antlion Chinese APT Uses Custom Backdoor to Target Financial Institutions in Taiwan", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "b1bd49f1ce653a470d0a8be83bb02b9140eeba34", "Date": "02/03/2022", "Year": "2022"}, {"Filename": "PaloAltoNetworks_Russias-Gamaredon-PrimitiveBear-Targeting-Ukraine(02-03-2022)", "Title": "G<PERSON>redon (Primitive Bear) Russian APT Group Actively Targeting Ukraine", "Source": "palo alto networks", "Link": "https://app.box.com/s/********************************", "SHA-1": "80649c8b4cfc9987a5b747021917cd53f2c433e5", "Date": "02/03/2022", "Year": "2022"}, {"Filename": "Microsoft_ACTINIUM-Ukrainian-organizations(02-04-2022)", "Title": "ACTINIUM targets Ukrainian organizations", "Source": "microsoft", "Link": "https://app.box.com/s/********************************", "SHA-1": "58a7dc800d633da1fad1756815d0e71ad120b60f", "Date": "02/04/2022", "Year": "2022"}, {"Filename": "Kaspersky_Roaming-Mantis-reaches-Europe(02-07-2022)", "Title": "Roaming Mantis reaches Europe", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "137293cfe2aa7df93d22e44862dbfac8f6ff3aa5", "Date": "02/07/2022", "Year": "2022"}, {"Filename": "Proofpoint_UggBoots-Palestinian-Aligned-Espionage(02-08-2022)", "Title": "Ugg Boots 4 Sale: A Tale of Palestinian-Aligned Espionage", "Source": "proofpoint", "Link": "https://app.box.com/s/********************************", "SHA-1": "eb3ae8c7166d76aa564ab94457383f5321b45f81", "Date": "02/08/2022", "Year": "2022"}, {"Filename": "SentinalOne_modified-elephant-apt(02-09-2022)", "Title": "Modified Elephant APT and a Decade of Fabricating Evidence", "Source": "SentinalOne", "Link": "https://app.box.com/s/********************************", "SHA-1": "892e3677718b44d08c8b95f63867c55215931363", "Date": "02/09/2022", "Year": "2022"}, {"Filename": "HVSConsulting_EmissaryPanda(02-14-2022)", "Title": "The APT fallout of vulnerabilities such as ProxyLogon in Exchange (Hafnium), OGNL injection, and log4shell", "Source": "HVS Consulting", "Link": "https://app.box.com/s/********************************", "SHA-1": "957289a738576160892bcaca22df777d63d586ed", "Date": "02/14/2022", "Year": "2022"}, {"Filename": "Fortinet_DriveGuard-<PERSON>-Staff-Campaigns(02-15-2022)", "Title": "Guard Your Drive from DriveGuard: Moses Staff Campaigns Against Israeli Organizations Span Several Months", "Source": "fortinet", "Link": "https://app.box.com/s/********************************", "SHA-1": "d10d8afa48caf110f7e2261e6a5521231d96800d", "Date": "02/15/2022", "Year": "2022"}, {"Filename": "Proofpoint_Charting-TA2541s-Flight(02-15-2022)", "Title": "Charting TA2541's Flight", "Source": "proofpoint", "Link": "https://app.box.com/s/********************************", "SHA-1": "baccd5c5f70b4f98227f3628558b3e4a0d2ceb84", "Date": "02/15/2022", "Year": "2022"}, {"Filename": "Telsy_BabaDeda-LorecCPL-Outsteel-Ukraine(02-16-2022)", "Title": "BabaDeda and LorecCPL downloaders used to run Outsteel against Ukraine", "Source": "telsy", "Link": "https://app.box.com/s/********************************", "SHA-1": "4622da4f28cb5b28c61aaebc8eaa43ed4bfd45e2", "Date": "02/16/2022", "Year": "2022"}, {"Filename": "nsfocus_Lorec53-LoriBear-Ukraine(02-16-2022)", "Title": "APT Group LOREC53 (<PERSON>) Recently Launched A Large-Scale Cyber Attack On Ukraine", "Source": "nsfocus", "Link": "https://app.box.com/s/********************************", "SHA-1": "d883d94dd0cc7257751177e42a32bd5a85796b03", "Date": "02/16/2022", "Year": "2022"}, {"Filename": "alyac_NKorea-digital-asset-wallet-customer-center(02-16-2022)", "Title": "North Korea-linked APT attack found disguised as a digital asset wallet service customer center", "Source": "ESTSecurity", "Link": "https://app.box.com/s/********************************", "SHA-1": "fdf53fac0af9cced76179fb1b9df0ce009952e36", "Date": "02/16/2022", "Year": "2022"}, {"Filename": "Sentinelone_Log4j2-TunnelVision-Exploiting-VMware-Horizon(02-17-2022)", "Title": "Iranian-Aligned Threat Actor ", "Source": "SentinelOne", "Link": "https://app.box.com/s/********************************", "SHA-1": "29ba4e36eaa5cd7c1a612f10f7ba53fb275cd6e5", "Date": "02/17/2022", "Year": "2022"}, {"Filename": "Sentinelone_HermeticWiper-Ukraine(02-23-2022)", "Title": "HermeticWiper - New Destructive Malware Used In Cyber Attacks on Ukraine", "Source": "sentinelone", "Link": "https://app.box.com/s/********************************", "SHA-1": "6889710cb527eb5d941412a7145d68bbbfa12a10", "Date": "02/23/2022", "Year": "2022"}, {"Filename": "Mandiant_UNC2596-Observed-Leveraging-Vulnerabilities-Cuba-Ransomware(02-23-2022)", "Title": "UNC2596 Observed Leveraging Vulnerabilities to Deploy Cuba Ransomware", "Source": "Mandiant", "Link": "https://app.box.com/s/********************************", "SHA-1": "97d56a7fe645bf9914b5547b5efa6f3ef3234ac0", "Date": "02/23/2022", "Year": "2022"}, {"Filename": "CISA_AA22-055A_Iranian_Government-Sponsored_Actors_Conduct_Cyber_Operations(02-24-2022)", "Title": "Iranian Government-Sponsored Actors Conduct Cyber Operations Against Global Government and Commercial Networks", "Source": "cisa", "Link": "https://app.box.com/s/********************************", "SHA-1": "cd6ceecd02ed516cf106bf855e81fd26b1b51aed", "Date": "02/24/2022", "Year": "2022"}, {"Filename": "Symantec_Ukraine-Disk-wiping-Russian-Invasion(02-24-2022)", "Title": "Ukraine: Disk-wiping Attacks Precede Russian Invasion", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "63e5676ff2013e825dc31e0631369dd466bc4599", "Date": "02/24/2022", "Year": "2022"}, {"Filename": "PaloAltoNetworks_SockDetour-Fileless-Socketless-Backdoor(02-24-2022)", "Title": "SockDetour Backdoor Targets U.S. Defense Contractors", "Source": "palo alto networks", "Link": "https://app.box.com/s/********************************", "SHA-1": "7f8679add7d3c25bc6f32fb644a1ee78eb007c7b", "Date": "02/24/2022", "Year": "2022"}, {"Filename": "Mandiant_Telegram-Malware-Iranian-Activity(02-24-2022)", "Title": "Telegram Malware Spotted in Latest Iranian Cyber Espionage Activity", "Source": "mandiant", "Link": "https://app.box.com/s/********************************", "SHA-1": "20ceb7bd73b288a0f83d8e9c7c70f2f6bdb876a9", "Date": "02/24/2022", "Year": "2022"}, {"Filename": "Fortinet_Nobelium-Returns(02-24-2022)", "Title": "Nobelium Returns to the Political World Stage", "Source": "Fortinet", "Link": "https://app.box.com/s/********************************", "SHA-1": "dd9bef5bda6d31741d435529fc2193c89bdf4a19", "Date": "02/24/2022", "Year": "2022"}, {"Filename": "PaloAlto_Spear-Phishing-Ukraine-OutSteel(02-25-2022)", "Title": "OutSteel, SaintBot Delivered by <PERSON><PERSON> Phishing Attacks Targeting Ukraine", "Source": "Palo Alto Networks", "Link": "https://app.box.com/s/********************************", "SHA-1": "60dcb5754259d9b9ef82db3a226b8f9409a6e7fb", "Date": "02/25/2022", "Year": "2022"}, {"Filename": "CISA_AA22-057A_Destructive_Malware_Targeting_Organizations_in_Ukraine(02-26-2022)", "Title": "Destructive Malware Targeting Organizations in Ukraine", "Source": "cisa", "Link": "https://app.box.com/s/********************************", "SHA-1": "4e0945984dd92c9f9cdcf37350d5df56e28311f8", "Date": "02/26/2022", "Year": "2022"}, {"Filename": "ESET_IsaacWiper-HermeticWizard-targeting-Ukraine(03-01-2022)", "Title": "IsaacWiper and HermeticWizard: New wiper and worm targeting Ukraine", "Source": "ESET", "Link": "https://app.box.com/s/********************************", "SHA-1": "3c60522829454e2b55e2f2544e24668948bf980a", "Date": "03/01/2022", "Year": "2022"}, {"Filename": "Proofpoint_Asylum-Ambuscade-Ukrainian-Military-Emails-Target-European(03-01-2022)", "Title": "Asylum Ambuscade: State Actor Uses Compromised Private Ukrainian Military Emails to Target European Governments and Refugee Movement", "Source": "proofpoint", "Link": "https://app.box.com/s/********************************", "SHA-1": "2972b297a603cd2b70e365ce6db4d58846cdb802", "Date": "03/01/2022", "Year": "2022"}, {"Filename": "RecordedFuture_mtp-2022-0302(03-02-2022)", "Title": "HermeticWiper and PartyTicket Targeting Computers in Ukraine", "Source": "RecordedFuture", "Link": "https://app.box.com/s/********************************", "SHA-1": "afd4c1685181d626ba8169f95ba50c655dd8d33d", "Date": "03/02/2022", "Year": "2022"}, {"Filename": "Ahn<PERSON><PERSON>_Malicious-Hangul-disguised-pressreleases-presidential-election(03-03-2022)", "Title": "Distribution of malicious Hangul documents disguised as press releases for the 20th presidential election", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "4ed602d14da6ffdcef5a9a04f1adfe906f2bca2e", "Date": "03/03/2022", "Year": "2022"}, {"Filename": "Mandiant_Summary-APT41-Targeting-US-State-Governments(03-08-2022)", "Title": "A Summary of APT41 Targeting U.S. State Governments", "Source": "Mandiant", "Link": "https://app.box.com/s/********************************", "SHA-1": "4625a11ec7fb6b97ac5e4882aec5c1a143483bef", "Date": "03/08/2022", "Year": "2022"}, {"Filename": "Lab52_Lazyscripters-double-compromise-single-obfuscation(03-09-2022)", "Title": "Very very lazy <PERSON><PERSON><PERSON><PERSON><PERSON>'s scripts: double compromise in a single obfuscation", "Source": "Lab52", "Link": "https://app.box.com/s/********************************", "SHA-1": "7f124e29c43dfa42c4e4d87b26bd3e18d15d21d4", "Date": "03/09/2022", "Year": "2022"}, {"Filename": "Cisco_Iranian-MuddyWater-regionally-focused-subgroups(03-10-2022)", "Title": "Iranian linked conglomerate MuddyWater comprised of regionally focused subgroups", "Source": "talosintelligence", "Link": "https://app.box.com/s/********************************", "SHA-1": "367ba69c8a0502752e921709a50e98b72a8903da", "Date": "03/10/2022", "Year": "2022"}, {"Filename": "CISA_AA22-074A_Russian_State-Sponsored_Cyber_Actors_Gain_Network_Access_by_Exploiting_Default_MFA_and_PrintNightmare(03-15-2022)", "Title": "Russian State-Sponsored Cyber Actors Gain Network Access by Exploiting Default Multifactor Authentication Protocols and ", "Source": "CISA", "Link": "https://app.box.com/s/********************************", "SHA-1": "8df1840dd0b1f01264da0b9d90fa6d7eabff892d", "Date": "03/15/2022", "Year": "2022"}, {"Filename": "Sentinelone_UAC-0056-Targeting-Ukraine-Fake-Translation-Software(03-15-2022)", "Title": "Threat Actor UAC-0056 Targeting Ukraine with Fake Translation Software", "Source": "Sentinelone", "Link": "https://app.box.com/s/********************************", "SHA-1": "000215f424ffd26d7d1b06832490fec8a501e8eb", "Date": "03/15/2022", "Year": "2022"}, {"Filename": "Mandiant_An-Overview-of-UNC2891(03-16-2022)", "Title": "An Overview of UNC2891", "Source": "Mandiant", "Link": "https://app.box.com/s/********************************", "SHA-1": "bf8e86195d1ccbd22938606a9f2f9086f91c6dfc", "Date": "03/16/2022", "Year": "2022"}, {"Filename": "Proofpoint_Serpent-Backdoor-Targets-French-Entities(03-21-2022)", "Title": "Serpent, No Swiping! New Backdoor Targets French Entities with Unique Attack Chain", "Source": "Proofpoint", "Link": "https://app.box.com/s/********************************", "SHA-1": "6fa14891cb1ded25243eedebcf706769b9db24d1", "Date": "03/21/2022", "Year": "2022"}, {"Filename": "TheDFIRReport_APT35-Automates-Initial-Access-Using-ProxyShell(03-21-2022)", "Title": "APT35 Automates Initial Access Using ProxyShell", "Source": "TheDFIRreport", "Link": "https://app.box.com/s/********************************", "SHA-1": "8b002310687d229d011cb32a6c888ffc2469c491", "Date": "03/21/2022", "Year": "2022"}, {"Filename": "Avast_Operation-Dragon-Castling-APT-targeting-betting-companies(03-22-2022)", "Title": "Operation Dragon Castling: APT group targeting betting companies", "Source": "<PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "57578072d22b95901e875f19cfb7ead3002d07f1", "Date": "03/22/2022", "Year": "2022"}, {"Filename": "CISA_AA22-054A New Sandworm Malware Cyclops Blink Replaces VPN Filter(03-23-2022)", "Title": "New Sandworm Malware Cyclops Blink Replaces VPNFilter", "Source": "CISA", "Link": "https://app.box.com/s/********************************", "SHA-1": "1e7b9c2fb9c9533572df9070f118b15bdbcb6104", "Date": "03/23/2022", "Year": "2022"}, {"Filename": "DrWeb-telecom_research_en(03-24-2022)", "Title": "Study of an APT attack on a telecommunications company in Kazakhstan", "Source": "DrWeb", "Link": "https://app.box.com/s/********************************", "SHA-1": "c848df52070b8baac8ccb65d4a223c2370e75469", "Date": "03/24/2022", "Year": "2022"}, {"Filename": "cert.gov.ua-CERT-UA-4293(03-28-2022)", "Title": "UAC-0056 cyberattack on Ukrainian authorities using GraphSteel and GrimPlant malware", "Source": "CERT_UA", "Link": "https://app.box.com/s/********************************", "SHA-1": "e557448d4f67e12c6427dd89258b166a91b04ba7", "Date": "03/28/2022", "Year": "2022"}, {"Filename": "Malwarebytes_spear-phishing-targets-Russian-dissidents(03-29-2022)", "Title": "New spear phishing campaign targets Russian dissidents", "Source": "malwarebytes", "Link": "https://app.box.com/s/********************************", "SHA-1": "e4ea89258b2eae980abd091e9b813783422fb4e4", "Date": "03/29/2022", "Year": "2022"}, {"Filename": "Ahnlab-APT-disguised-NorthKorean-defector-resume-VBS-script(03-29-2022)", "Title": "APT attack disguised as North Korean defector resume format ", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "c5d7e2c9666e64d3d2c303b365b38a07e24026a6", "Date": "03/29/2022", "Year": "2022"}, {"Filename": "CiscoTalos_TransparentTribe-bespoke-malware-target-Indian-gov-officials(03-29-2022)", "Title": "Transparent Tribe campaign uses new bespoke malware to target Indian government officials", "Source": "Cisco", "Link": "https://app.box.com/s/********************************", "SHA-1": "0c0ff45c3ade035d3827a8850ac0cc6c3b067446", "Date": "03/29/2022", "Year": "2022"}, {"Filename": "QAX_VajraEleph-Cyber-espionage-against-Pakistani-military(03-30-2022)", "Title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from South Asia - Cyber espionage against Pakistani military personnel revealed", "Source": "QAX", "Link": "https://app.box.com/s/********************************", "SHA-1": "a1c958967e9f8aa2caf1633099e2223b83b6ebd0", "Date": "03/30/2022", "Year": "2022"}, {"Filename": "Fortinet_DeepPanda-Log4Shell-Digitally-Signed-Fire-Chili-Rootkits(03-30-2022)", "Title": "New Milestones for Deep Panda: Log4Shell and Digitally Signed Fire Chili Rootkits", "Source": "Fortinet", "Link": "https://app.box.com/s/********************************", "SHA-1": "065fe12cd3ae9dd718d0d2d9032ff16f2e12d7a8", "Date": "03/30/2022", "Year": "2022"}, {"Filename": "<PERSON><PERSON><PERSON>_<PERSON>-Trojanized-DeFi-delivering-malware(03-31-2022)", "Title": "Lazarus Trojanized DeFi app for delivering malware", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "3ca55acc1173d75d9cbf3beb66dfb32e7503e957", "Date": "03/31/2022", "Year": "2022"}, {"Filename": "Sentinelone_AcidRain-Wiper-Rains-Down-Europe(03-31-2022)", "Title": "AcidRain: A Modem Wiper Rains Down on Europe", "Source": "Sentinelone", "Link": "https://app.box.com/s/********************************", "SHA-1": "3e9f3cd1c4cf394a7bdaa9f918b6bc0407de7346", "Date": "03/31/2022", "Year": "2022"}, {"Filename": "RecordedFuture_Chinese-targeting-IndianPowerGrid-ta-2022-0406(04-06-2022)", "Title": "Continued Targeting of Indian Power Grid Assets by Chinese State-Sponsored Activity Group", "Source": "recordedfuture", "Link": "https://app.box.com/s/********************************", "SHA-1": "0b1b32b1d1f2cf93f41966c2eb607df4845a1c5f", "Date": "04/06/2022", "Year": "2022"}, {"Filename": "Qianxin_Analysis of the Suspected Lazarus Attack Activities against South Korean Companies(04-11-2022)", "Title": "Snow abuse and gluttony: Analysis of suspected Lazarus attack activities against Korean companies", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "d2c884f5d1d49194f6688d640bb71dbb50ad332e", "Date": "04/11/2022", "Year": "2022"}, {"Filename": "Microsoft_Tarrask-malware-scheduled-tasks-defense-evasion(04-12-2022)", "Title": "Tarrask malware uses scheduled tasks for defense evasion", "Source": "microsoft", "Link": "https://app.box.com/s/********************************", "SHA-1": "0d80c5b363a71f4f69736cddf9b9bc2e1a632697", "Date": "04/12/2022", "Year": "2022"}, {"Filename": "cert.gov.ua-CERT-UA-4435(04-12-2022)", "Title": "Cyberattack by Sandworm Group (UAC-0082) on energy facilities of Ukraine using malicious programs INDUSTROYER2 and CADDYWIPER", "Source": "CERT-UA", "Link": "https://app.box.com/s/********************************", "SHA-1": "69ff06f54e2333e8102da79694ce8cb2429fac5d", "Date": "04/12/2022", "Year": "2022"}, {"Filename": "cert.gov.ua-CERT-UA-4435(04-12-2022)", "Title": "Cyberattack by Sandworm Group (UAC-0082) on energy facilities of Ukraine using malicious programs INDUSTROYER2 and CADDYWIPER", "Source": "CERT-UA", "Link": "https://app.box.com/s/********************************", "SHA-1": "69ff06f54e2333e8102da79694ce8cb2429fac5d", "Date": "04/12/2022", "Year": "2022"}, {"Filename": "Symantec_Lazarus-Targets-Chemical-Sector(04-14-2022)", "Title": "Lazarus Targets Chemical Sector", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "be31a634d4139e6223c50cc18cc69916ba585475", "Date": "04/14/2022", "Year": "2022"}, {"Filename": "cert.gov.ua-CERT-UA-4464(04-14-2022)", "Title": "Cyberattack on state organizations of Ukraine using the malicious program IcedID", "Source": "CERT-UA", "Link": "https://app.box.com/s/********************************", "SHA-1": "24129608075aa7b7a8e3a726a5b009c30bae881e", "Date": "04/14/2022", "Year": "2022"}, {"Filename": "inquest_Nobelium-Israeli-Embassy-Maldoc(04-18-2022)", "Title": "Nobelium - Israeli Embassy Maldoc", "Source": "Inquest", "Link": "https://app.box.com/s/********************************", "SHA-1": "609414f31b49c22b7d212df8686ab2a5d1daa9d6", "Date": "04/18/2022", "Year": "2022"}, {"Filename": "cert.gov.ua-CERT-UA-4490(04-18-2022)", "Title": "Cyberattack on state organizations of Ukraine using the topic \"Azovstal\"", "Source": "CERT-UA", "Link": "https://app.box.com/s/********************************", "SHA-1": "6c477457f9443000c20ae8292713e926dc12ad97", "Date": "04/18/2022", "Year": "2022"}, {"Filename": "Ahnlab-Lazarus-attack-group-exploits-INITECH-process(04-18-2022)", "Title": "Lazarus attack group that exploits the INITECH process", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "bdbe3c5510aab146d327e72ab50768503021175b", "Date": "04/18/2022", "Year": "2022"}, {"Filename": "CISA_AA22-108A-TraderTraitor-North_Korea_APT_Targets_Blockchain_Companies(04-18-2022)", "Title": "TraderTraitor: North Korean State-Sponsored APT Targets Blockchain Companies", "Source": "CISA", "Link": "https://app.box.com/s/********************************", "SHA-1": "49f91b6196d02311aa43b5c72bbd28b7d1d3b905", "Date": "04/18/2022", "Year": "2022"}, {"Filename": "Symantec-Shuckworm-Espionage-Group-Campaign-Against-Ukraine(04-20-2022)", "Title": "Shuckworm: Espionage Group Continues Intense Campaign Against Ukraine", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "d6d27a3621ad8ef77df6c6c9644d8deb4a32c2ba", "Date": "04/20/2022", "Year": "2022"}, {"Filename": "Stairwell-threat-report-The-ink-stained-trail-of-GOLDBACKDOOR(04-21-2022)", "Title": "The ink-stained trail of GOLDBACKDOOR", "Source": "stairwell", "Link": "https://app.box.com/s/********************************", "SHA-1": "095c763cb66c58a7f61e97b0dcd7328ae0a51d59", "Date": "04/21/2022", "Year": "2022"}, {"Filename": "Zscaler-Naver-ending-game-Lazarus-APT(04-26-2022)", "Title": "A \"Naver\" ending game of Lazarus APT", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "e2b266f4afeaf7b3f608df75ab62abdc23f7941f", "Date": "04/26/2022", "Year": "2022"}, {"Filename": "Symantec_Stonefly-North Korea-Continues-High-value-Targets(04-27-2022)", "Title": "Stonefly: North Korea-linked Spying Operation Continues to Hit High-value Targets", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "ec0ff900599235d4e4103a9c6ea980ff54cc0a2c", "Date": "04/27/2022", "Year": "2022"}, {"Filename": "nccgroup_LAPSUS-Recent-TTPs(04-28-2022)", "Title": "LAPSUS$: Recent techniques, tactics and procedures", "Source": "nccgroup", "Link": "https://app.box.com/s/********************************", "SHA-1": "e8c33cefaf9dc42acf2d668cf78d781fdf17c240", "Date": "04/28/2022", "Year": "2022"}, {"Filename": "CISA_AA22-057A_Destructive_Malware_Targeting_Organizations_in_Ukraine-r2(04-28-2022)", "Title": "Update: Destructive Malware Targeting Organizations in Ukraine", "Source": "cisa", "Link": "https://app.box.com/s/********************************", "SHA-1": "1d7d0bb200a0725926d92c5bea1f452453efcfec", "Date": "04/28/2022", "Year": "2022"}, {"Filename": "Mandiant_UNC3524-<PERSON>-<PERSON>-<PERSON><PERSON>(05-02-2022)", "Title": "UNC3524: Eye Spy on Your Email", "Source": "Mandiant", "Link": "https://app.box.com/s/********************************", "SHA-1": "88eac247905e7a62616e25117c29997cb3660307", "Date": "05/02/2022", "Year": "2022"}, {"Filename": "CiscoTalos_GamaredonAPT-targets-Ukrainian-new-campaign(09-15-2022)", "Title": "Gamaredon APT targets Ukrainian government agencies in new campaign", "Source": "CiscoTalos", "Link": "https://app.box.com/s/********************************", "SHA-1": "abee4fba459156eb4b681f95337c5fade2e24385", "Date": "09/15/2022", "Year": "2022"}, {"Filename": "Checkpoint_Cloud-Atlas-targets-Russia-Belarus-amid-Ukraine(12-09-2022)", "Title": "Cloud Atlas targets entities in Russia and Belarus amid the ongoing war in Ukraine", "Source": "checkpoint", "Link": "https://app.box.com/s/********************************", "SHA-1": "ce2bb5a0741a4bdd6f9cb96a205603388c2e8323", "Date": "12/09/2022", "Year": "2022"}, {"Filename": "PaloAlto_Russias-TridentUrsa-GamaredonAPT-CyberConflic-Operations-Ukraine(12-20-2022)", "Title": "Russia's Trident Ursa (aka Gamaredon APT) Cyber Conflict Operations Unwavering Since Invasion of Ukraine", "Source": "Palo Alto", "Link": "https://app.box.com/s/********************************", "SHA-1": "7ffa8cc48d900c15e6ac08b104610f82011496c5", "Date": "12/20/2022", "Year": "2022"}, {"Filename": "Checkpoint_BlindEagle-Targeting-Ecuador-Sharpened-Tools(01-05-2023)", "Title": "BlindEagle Targeting Ecuador With Sharpened Tools", "Source": "checkpoint", "Link": "https://app.box.com/s/********************************", "SHA-1": "3a904da819b8adb107998f6408249cf29ce950cb", "Date": "01/05/2023", "Year": "2023"}, {"Filename": "Group-ib_DarkPink(01-11-2023)", "Title": "Dark Pink: New APT hitting Asia-Pacific, Europe that goes deeper and darker", "Source": "Group-ib", "Link": "https://app.box.com/s/********************************", "SHA-1": "e5c9ee752a09cc290bd5bd6c40b9c14241586ff7", "Date": "01/11/2023", "Year": "2023"}, {"Filename": "Eclecticiq_MustangPandaAPT-EUThemed-Lure-PlugX(02-02-2023)", "Title": "Mustang Panda APT Group Uses European Commission-Themed Lure to Deliver PlugX Malware", "Source": "Eclecticiq", "Link": "https://app.box.com/s/********************************", "SHA-1": "f9dc47847698fb913f004b17525a16d37a7c711c", "Date": "02/02/2023", "Year": "2023"}, {"Filename": "Trendmicro_New-APT34-Malware-Targets-MiddleEast(02-02-2023)", "Title": "New APT34 Malware Targets The Middle East", "Source": "Trend Micro", "Link": "https://app.box.com/s/********************************", "SHA-1": "8e4de1d12b7d3b27bb649689bab287dcf1f5fe46", "Date": "02/02/2023", "Year": "2023"}, {"Filename": "Symantec_Graphiron-Russian-Malware-Deployed-Against-Ukraine(02-08-2023)", "Title": "Graphiron: New Russian Information Stealing Malware Deployed Against Ukraine", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "232cfeb4ee7a1e9aae111eebb5574dcc970e0d9b", "Date": "02/08/2023", "Year": "2023"}, {"Filename": "Ahnlab_Dalbit-m00nlight-Chinese-APT-Campaign(02-13-2023)", "Title": "Dalbit (m00nlight): Chinese Hacker Group's APT Attack Campaign", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "f8464b2e4c8ac43e64aaf9c3034286da17a2b91b", "Date": "02/13/2023", "Year": "2023"}, {"Filename": "Checkpoint_Operation-Silent-Watch-Desktop-Surveillance-Azerbaijan-Armenia(02-16-2023)", "Title": "Operation Silent Watch: Desktop Surveillance in Azerbaijan and Armenia", "Source": "Checkpoint", "Link": "https://app.box.com/s/********************************", "SHA-1": "b897b76560babe5ea2af500b12ce15160c39944f", "Date": "02/16/2023", "Year": "2023"}, {"Filename": "Ahnlab_HWP-Malware-Steganography-ScarCruft(02-21-2023)", "Title": "HWP Malware Using the Steganography Technique: RedEyes (ScarCruft)", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "18f2fa0dd7b7178ba88d5a5d412d86bf3df43c28", "Date": "02/21/2023", "Year": "2023"}, {"Filename": "Symantec_Hydrochasma-UnknownGroup-Targets-Medical-Shipping-Asia(02-22-2023)", "Title": "Hydrochasma: Previously Unknown Group Targets Medical and Shipping Organizations in Asia", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "5669fa6adf6841e8d550e03636e13e39cc8e4560", "Date": "02/22/2023", "Year": "2023"}, {"Filename": "ESET_WinorDLL64-Lazarus-arsenal(02-23-2023)", "Title": "WinorDLL64: A backdoor from the vast Lazarus arsenal?", "Source": "ESET", "Link": "https://app.box.com/s/********************************", "SHA-1": "29308a374352e9b10395a445b54d29c44fa1a954", "Date": "02/23/2023", "Year": "2023"}, {"Filename": "Blackberry_BlindEagle-Fake-UUE-Fsociety-Target-Colombia(02-27-2023)", "Title": "Blind Eagle Deploys Fake UUE Files and Fsociety to Target Colombia", "Source": "Blackberry", "Link": "https://app.box.com/s/********************************", "SHA-1": "669282e93ad3ee2f09ca00538a9e9f9d474dcb9b", "Date": "02/27/2023", "Year": "2023"}, {"Filename": "Ahnlab_Lazarus-using-public-certificate-vulnerability(02-27-2023)", "Title": "Lazarus group using public certificate vulnerability", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "9f0ac45dbfeeded1ad46f09e104640566c8f7eda", "Date": "02/27/2023", "Year": "2023"}, {"Filename": "Symantec_Blackfly_Materials_Technology(02-28-2023)", "Title": "Blackfly: Espionage Group Targets Materials Technology", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "4267c0947c6c196ac1f13f4ae440231f1828a67e", "Date": "02/28/2023", "Year": "2023"}, {"Filename": "Trendmicro_IronTigers-SysUpdate-Adds-Linux-Targeting(03-01-2023)", "Title": "Iron Tiger's SysUp<PERSON> Reappears, Adds Linux Targeting", "Source": "Trend Micro", "Link": "https://app.box.com/s/********************************", "SHA-1": "5af93af18d82bd2525635dc8a5d29126e05207ee", "Date": "03/01/2023", "Year": "2023"}, {"Filename": "ESET_MQsTTang-MustangPandas-backdoor-Qt-MQTT(03-02-2023)", "Title": "MQsTTang: Mustang Panda's latest backdoor treads new ground with Qt and MQTT", "Source": "ESET", "Link": "https://app.box.com/s/********************************", "SHA-1": "a315b44bfa8879deee5d9ee550e6bdf1516c6258", "Date": "03/02/2023", "Year": "2023"}, {"Filename": "Proofpoint_Russia-Aligned-TA499-Beleaguers-Targets-Video-Call-Requests(03-07-2023)", "Title": "Don't Answer That! Russia-Aligned TA499 Beleaguers Targets with Video Call Requests", "Source": "Proofpoint", "Link": "https://app.box.com/s/********************************", "SHA-1": "12f3a174311f611263f75c3b1d62f69b11c53560", "Date": "03/07/2023", "Year": "2023"}, {"Filename": "Checkpoint_Chinese-Espionage-Southeast-Asian-Government-Entities(03-07-2023)", "Title": "Pandas with a Soul: Chinese Espionage Attacks Against Southeast Asian Government Entities", "Source": "Checkpoint", "Link": "https://app.box.com/s/********************************", "SHA-1": "d1a6a9b7fbd5a94cdd7b325883da3e413d4f8ffc", "Date": "03/07/2023", "Year": "2023"}, {"Filename": "Mandiant_LIGHTSHOW-1-North-Koreas-UNC2970(03-09-2023)", "Title": "Stealing the LIGHTSHOW (Part One) - North Korea's UNC2970", "Source": "Mandiant", "Link": "https://app.box.com/s/********************************", "SHA-1": "e9325a67d4681c8cdfc1e2a18783142e2e74826c", "Date": "03/09/2023", "Year": "2023"}, {"Filename": "Mandiant_LIGHTSHOW-2-LIGHTSHIFT-and-LIGHTSHOW(03-09-2023)", "Title": "Stealing the LIGHTSHOW (Part Two) - LIGHTSHIFT and LIGHTSHOW", "Source": "Mandiant", "Link": "https://app.box.com/s/********************************", "SHA-1": "604ce904b72ea33febb00802b7b26f4d782f6f67", "Date": "03/09/2023", "Year": "2023"}, {"Filename": "360_APT-C-56-TransparentTribe-camouflage-campaign(02-13-2023)", "Title": "Analysis of APT-C-56 (Transparent Tribe) camouflage resume attack campaign", "Source": "CoreSec360", "Link": "https://app.box.com/s/********************************", "SHA-1": "39ab8916e64fe878e3198b82bc0bfd0b78c8e8cd", "Date": "03/13/2023", "Year": "2023"}, {"Filename": "ESET_Tick-APT-compromise-DLP-developer-East-Asia(03-14-2023)", "Title": "The slow Ticking time bomb: Tick APT group compromise of a DLP software developer in East Asia", "Source": "ESET", "Link": "https://app.box.com/s/********************************", "SHA-1": "23219da0963e95373d5dacba4348f1ccd274fd59", "Date": "03/14/2023", "Year": "2023"}, {"Filename": "Mandiant_Fortinet-Zero-Day-Suspected-Chinese-Operation(03-16-2023)", "Title": "Fortinet Zero-Day and Custom Malware Used by Suspected Chinese Actor in Espionage Operation", "Source": "mandiant", "Link": "https://app.box.com/s/********************************", "SHA-1": "d4d5053d7057f5fd24a4e431de95c2efc33b3a59", "Date": "03/16/2023", "Year": "2023"}, {"Filename": "ESET_apt-activity-report-q2-2023-q3-2023(10-26-2023)", "Title": "ESET APT Activity Report Q2-Q3 2023", "Source": "ESET", "Link": "https://app.box.com/s/********************************", "SHA-1": "7873f4b161d89a40f97a4b4104f212f4d556459a", "Date": "10/26/2023", "Year": "2023"}, {"Filename": "Ka<PERSON>sky_cascade-of-compromise-unveiling-<PERSON>-new-campaign(10-27-2023)", "Title": "A cascade of compromise: unveiling <PERSON>' new campaign", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "d88076abd0fbde4927a2948ca8065659e9ba6752", "Date": "10/27/2023", "Year": "2023"}, {"Filename": "CoreSec360_APT-C-36-Blind<PERSON>agle-Amadey-botnet(10-31-2023)", "Title": "Analysis of activities of suspected APT-C-36 (Blind Eagle) organization launching Amadey botnet Trojan", "Source": "CoreSec360", "Link": "https://app.box.com/s/********************************", "SHA-1": "5e1d8f90ff100844c70bc6c663ffe26771881cd7", "Date": "10/31/2023", "Year": "2023"}, {"Filename": "Checkpoint_Fro-Albania-to-MiddleEast-Sc<PERSON>red-Mantic<PERSON>(10-31-2023)", "Title": "From Albania To The Middle East: The Scarred Manticore Is Listening", "Source": "Checkpoint", "Link": "https://app.box.com/s/********************************", "SHA-1": "18c0a89c955b06ef112a48590e14879c919c72b4", "Date": "10/31/2023", "Year": "2023"}, {"Filename": "Deepinstinct_MuddyWater-spearphishing-new-TTPs(11-01-2023)", "Title": "MuddyWater eN-Able spear-phishing with new TTPs", "Source": "deepinstinct", "Link": "https://app.box.com/s/********************************", "SHA-1": "d3bf493f5cb37521928b92e9eee3af89d0f3bf82", "Date": "11/01/2023", "Year": "2023"}, {"Filename": "Kaspersky_Modern-Asian-APT-groups-TTPs_report_eng(11-09-2023)", "Title": "Modern Asia APT groups TTPs", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "ab3211cc642317a62c5330b640f24f08c3efb090", "Date": "11/09/2023", "Year": "2023"}, {"Filename": "Kaspersky_HrServ-webshell-in-APT-attack(11-22-2023)", "Title": "HrServ - Previously unknown web shell used in APT attack", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "312885ec160f68228c8ec450e3f61b54a25e17d5", "Date": "11/22/2023", "Year": "2023"}, {"Filename": "Blackberry_AeroBlade-Targeting-US-Aerospace-Industry(11-30-2023)", "Title": "AeroBlade on the Hunt Targeting the U.S. Aerospace Industry", "Source": "Blackberry", "Link": "https://app.box.com/s/********************************", "SHA-1": "ef854d3ab06b7acc16e2ab04a760468966d07805", "Date": "11/30/2023", "Year": "2023"}, {"Filename": "PaloAlto_NewToolSet-Organizations-in-Middle-East-Africa-and-US(12-01-2023)", "Title": "New Tool Set Found Used Against Organizations in the Middle East, Africa and the US", "Source": "Palo Alto Networks", "Link": "https://app.box.com/s/********************************", "SHA-1": "e16339ec8a52ad200eae8d0fb6be95550d8f9c07", "Date": "12/01/2023", "Year": "2023"}, {"Filename": "Ahnlab_Kimsuky-Group-AutoIt-Malware-RftRAT-Amadey(12-08-2023)", "Title": "Kimsuky Group Uses AutoIt to Create Malware (RftRAT, Amadey)", "Source": "<PERSON><PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "a224702fbdf785b482d562117dddf5d4fd848208", "Date": "12/08/2023", "Year": "2023"}, {"Filename": "ITG05_operations_leverage_Israel-Hamas_conflict_lures_to_deliver_Headlace_malware(12-08-2023)", "Title": "ITG05 operations leverage Israel-Hamas conflict lures to deliver Headlace malware", "Source": "IBM", "Link": "https://app.box.com/s/********************************", "SHA-1": "c4e23fff156bef106fb15a96734b9fcf44d37061", "Date": "12/08/2023", "Year": "2023"}, {"Filename": "CISA_aa23-347a-russian-foreign-intelligence-service-svr-exploiting-jetbrains-teamcity-cve-globally_0(12-13-2023)", "Title": "Russian Foreign Intelligence Service (SVR) Exploiting JetBrains TeamCity CVE Globally", "Source": "CISA", "Link": "https://app.box.com/s/********************************", "SHA-1": "443181071d70821ccd4f78533f8b6b9947dc4b46", "Date": "12/13/2023", "Year": "2023"}, {"Filename": "Sentinelone_GazaCybergang -UnifiedFront-Targeting-Opposition(12-14-2023)", "Title": "Gaza Cybergang Unified Front Targeting Hamas Opposition", "Source": "Sentinelone", "Link": "https://app.box.com/s/********************************", "SHA-1": "e8a83611d700dbc67bff004984b05714471b00c4", "Date": "12/14/2023", "Year": "2023"}, {"Filename": "ESET_OilRig-cloud-service-powered-downloaders(12-14-2023)", "Title": "OilRig's persistent attacks using cloud service-powered downloaders", "Source": "ESET", "Link": "https://app.box.com/s/********************************", "SHA-1": "1c9fb3d84f4702a0ae479b9e8f5c61ed27547d74", "Date": "12/14/2023", "Year": "2023"}, {"Filename": "Symantec_Seedworm-Iranian-Hackers-Target-Telecoms-Orgs-North-East-Africa(12-19-2023)", "Title": "Seedworm: Iranian Hackers Target Telecoms Orgs in North and East Africa", "Source": "Symantec", "Link": "https://app.box.com/s/********************************", "SHA-1": "247bc49a579b858eb7c50a9a6cb23ab022c8e1d2", "Date": "12/19/2023", "Year": "2023"}, {"Filename": "Netskope_Nim-based-Campaign-Word-Docs-Impersonate-Nepali-Gov(12-20-2023)", "Title": "A Look at the Nim-based Campaign Using Microsoft Word Docs to Impersonate the Nepali Government", "Source": "Netskope", "Link": "https://app.box.com/s/********************************", "SHA-1": "3f76b2e6e9861c0c3e4205dfeb5a9868539f4bb6", "Date": "12/20/2023", "Year": "2023"}, {"Filename": "CERT-UA-8338(12-21-2023)", "Title": "New UAC-0050 attack using RemcosRAT", "Source": "CERT-UA", "Link": "https://app.box.com/s/********************************", "SHA-1": "0d7df9ae534779b6ee238c2d882e75e7b80797e0", "Date": "12/21/2023", "Year": "2023"}, {"Filename": "Deepinstinct_TA-UAC-0099-Continues-Target-Ukraine(12-21-2023)", "Title": "Threat Actor 'UAC-0099' Continues to Target Ukraine", "Source": "Deepinstinct", "Link": "https://app.box.com/s/********************************", "SHA-1": "b0cce40bbc4e54a2742d25794bc5311cd958c2b2", "Date": "12/21/2023", "Year": "2023"}, {"Filename": "Kaspersky_OperationTriangulation-TheLastMystery(12-27-2023)", "Title": "Operation Triangulation: The last (hardware) mystery", "Source": "<PERSON><PERSON><PERSON>", "Link": "https://app.box.com/s/********************************", "SHA-1": "837998879b95838c33584012305e7d1fd23827ff", "Date": "12/27/2023", "Year": "2023"}, {"Filename": "CERT-UA-8399(12-28-2023)", "Title": "APT28: From Initial Damage to Domain Controller Threats in an Hour", "Source": "CERT-UA", "Link": "https://app.box.com/s/********************************", "SHA-1": "655acdbba1095c8339c40f02d2af3d3da50d22c4", "Date": "12/28/2023", "Year": "2023"}, {"Filename": "Cyble_OperationShadowCat-Targeting-Indian-Political-Observers(07-24-2024)", "Title": "Operation ShadowCat: Targeting Indian Political Observers via a Stealthy RAT", "Source": "cyble", "Link": "https://app.box.com/s/********************************", "SHA-1": "0b7549d2bf3b39256c32f3b4924113d0fec9c817", "Date": "07/24/2024", "Year": "2024"}]