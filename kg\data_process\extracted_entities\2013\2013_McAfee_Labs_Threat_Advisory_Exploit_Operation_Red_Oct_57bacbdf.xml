<Entitys>
    <Entity>
        <EntityId>entity_1</EntityId>
        <EntityName>Red October</EntityName>
        <EntityType>attcker</EntityType>
        <EntitySubType>org</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
            <Label>TA0002</Label>
            <Label>TA0006</Label>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>1</Time>
        </Times>
        <Properties>
            <Property name="country">Russia</Property>
            <Property name="active_since">2009</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_2</EntityId>
        <EntityName>Operation Red October</EntityName>
        <EntityType>event</EntityType>
        <EntitySubType>event</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
            <Label>TA0002</Label>
            <Label>TA0006</Label>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>1</Time>
        </Times>
        <Properties>
            <Property name="date">January 18, 2013</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_3</EntityId>
        <EntityName>Diplomatic and Government agencies</EntityName>
        <EntityType>vctim</EntityType>
        <EntitySubType>org</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
        </Labels>
        <Times>
            <Time>1</Time>
        </Times>
        <Properties>
            <Property name="sector">Government</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_4</EntityId>
        <EntityName>51EDEA56C1E83BCBC9F873168E2370AF</EntityName>
        <EntityType>file</EntityType>
        <EntitySubType>file</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
        </Labels>
        <Times>
            <Time>2</Time>
        </Times>
        <Properties>
            <Property name="hash">51EDEA56C1E83BCBC9F873168E2370AF</Property>
            <Property name="type">Document file</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_5</EntityId>
        <EntityName>CVE-2012-0158</EntityName>
        <EntityType>vul</EntityType>
        <EntitySubType>cve</EntitySubType>
        <Labels>
            <Label>TA0002</Label>
        </Labels>
        <Times>
            <Time>3</Time>
        </Times>
        <Properties>
            <Property name="product">MS Word</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_6</EntityId>
        <EntityName>CVE-2010-3333</EntityName>
        <EntityType>vul</EntityType>
        <EntitySubType>cve</EntitySubType>
        <Labels>
            <Label>TA0002</Label>
        </Labels>
        <Times>
            <Time>3</Time>
        </Times>
        <Properties>
            <Property name="product">MS Word</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_7</EntityId>
        <EntityName>CVE-2009-3129</EntityName>
        <EntityType>vul</EntityType>
        <EntitySubType>cve</EntitySubType>
        <Labels>
            <Label>TA0002</Label>
        </Labels>
        <Times>
            <Time>3</Time>
        </Times>
        <Properties>
            <Property name="product">MS Excel</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_8</EntityId>
        <EntityName>CVE-2011-3544</EntityName>
        <EntityType>vul</EntityType>
        <EntitySubType>cve</EntitySubType>
        <Labels>
            <Label>TA0002</Label>
        </Labels>
        <Times>
            <Time>3</Time>
        </Times>
        <Properties>
            <Property name="product">Java Rhino Script Engine</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_9</EntityId>
        <EntityName>msmx21.exe</EntityName>
        <EntityType>file</EntityType>
        <EntitySubType>file</EntitySubType>
        <Labels>
            <Label>TA0002</Label>
        </Labels>
        <Times>
            <Time>4</Time>
        </Times>
        <Properties>
            <Property name="location">%temp%</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_10</EntityId>
        <EntityName>svchost.exe</EntityName>
        <EntityType>file</EntityType>
        <EntitySubType>file</EntitySubType>
        <Labels>
            <Label>TA0003</Label>
        </Labels>
        <Times>
            <Time>5</Time>
        </Times>
        <Properties>
            <Property name="location">%ProgramFiles%\Windows NT\</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_11</EntityId>
        <EntityName>wsdktr.ltp</EntityName>
        <EntityType>file</EntityType>
        <EntitySubType>file</EntitySubType>
        <Labels>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>5</Time>
        </Times>
        <Properties>
            <Property name="location">%ProgramFiles%\Windows NT\</Property>
            <Property name="type">Encrypted payload</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_12</EntityId>
        <EntityName>nt-windows-online.com</EntityName>
        <EntityType>ioc</EntityType>
        <EntitySubType>domain</EntitySubType>
        <Labels>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>6</Time>
        </Times>
    </Entity>
    <Entity>
        <EntityId>entity_13</EntityId>
        <EntityName>nt-windows-update.com</EntityName>
        <EntityType>ioc</EntityType>
        <EntitySubType>domain</EntitySubType>
        <Labels>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>6</Time>
        </Times>
    </Entity>
    <Entity>
        <EntityId>entity_14</EntityId>
        <EntityName>nt-windows-check.com</EntityName>
        <EntityType>ioc</EntityType>
        <EntitySubType>domain</EntitySubType>
        <Labels>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>6</Time>
        </Times>
    </Entity>
    <Entity>
        <EntityId>entity_15</EntityId>
        <EntityName>csrss-check-new.com</EntityName>
        <EntityType>ioc</EntityType>
        <EntitySubType>domain</EntitySubType>
        <Labels>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>6</Time>
        </Times>
    </Entity>
    <Entity>
        <EntityId>entity_16</EntityId>
        <EntityName>qagi.exe</EntityName>
        <EntityType>file</EntityType>
        <EntitySubType>file</EntitySubType>
        <Labels>
            <Label>TA0002</Label>
        </Labels>
        <Times>
            <Time>7</Time>
        </Times>
        <Properties>
            <Property name="location">%Application Data%\Keucot\</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_17</EntityId>
        <EntityName>dezaa.ufy</EntityName>
        <EntityType>file</EntityType>
        <EntitySubType>file</EntitySubType>
        <Labels>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>7</Time>
        </Times>
        <Properties>
            <Property name="location">%Application Data%\Okurp\</Property>
            <Property name="type">Encrypted content</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_18</EntityId>
        <EntityName>29f2aad01fee3663.com</EntityName>
        <EntityType>ioc</EntityType>
        <EntitySubType>domain</EntitySubType>
        <Labels>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>8</Time>
        </Times>
    </Entity>
    <Entity>
        <EntityId>entity_19</EntityId>
        <EntityName>BackDoor-FJJ</EntityName>
        <EntityType>tool</EntityType>
        <EntitySubType>malware</EntitySubType>
        <Labels>
            <Label>TA0002</Label>
        </Labels>
        <Times>
            <Time>7</Time>
        </Times>
    </Entity>
    <Entity>
        <EntityId>entity_20</EntityId>
        <EntityName>Windows System</EntityName>
        <EntityType>env</EntityType>
        <EntitySubType>os</EntitySubType>
        <Labels>
            <Label>TA0002</Label>
        </Labels>
        <Times>
            <Time>3</Time>
        </Times>
    </Entity>
</Entitys>
<Relationships>
    <Relationship>
        <RelationshipId>relationship_1</RelationshipId>
        <RelationshipType>involve</RelationshipType>
        <Source>Operation Red October</Source>
        <Target>Red October</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_2</RelationshipId>
        <RelationshipType>involve</RelationshipType>
        <Source>Operation Red October</Source>
        <Target>Diplomatic and Government agencies</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_3</RelationshipId>
        <RelationshipType>use</RelationshipType>
        <Source>Red October</Source>
        <Target>51EDEA56C1E83BCBC9F873168E2370AF</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_4</RelationshipId>
        <RelationshipType>use</RelationshipType>
        <Source>Red October</Source>
        <Target>CVE-2012-0158</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_5</RelationshipId>
        <RelationshipType>use</RelationshipType>
        <Source>Red October</Source>
        <Target>CVE-2010-3333</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_6</RelationshipId>
        <RelationshipType>use</RelationshipType>
        <Source>Red October</Source>
        <Target>CVE-2009-3129</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_7</RelationshipId>
        <RelationshipType>use</RelationshipType>
        <Source>Red October</Source>
        <Target>CVE-2011-3544</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_8</RelationshipId>
        <RelationshipType>use</RelationshipType>
        <Source>Red October</Source>
        <Target>BackDoor-FJJ</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_9</RelationshipId>
        <RelationshipType>target</RelationshipType>
        <Source>Red October</Source>
        <Target>Diplomatic and Government agencies</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_10</RelationshipId>
        <RelationshipType>target</RelationshipType>
        <Source>Red October</Source>
        <Target>Windows System</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_11</RelationshipId>
        <RelationshipType>exploit</RelationshipType>
        <Source>CVE-2012-0158</Source>
        <Target>Windows System</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_12</RelationshipId>
        <RelationshipType>exploit</RelationshipType>
        <Source>CVE-2010-3333</Source>
        <Target>Windows System</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_13</RelationshipId>
        <RelationshipType>exploit</RelationshipType>
        <Source>CVE-2009-3129</Source>
        <Target>Windows System</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_14</RelationshipId>
        <RelationshipType>exploit</RelationshipType>
        <Source>CVE-2011-3544</Source>
        <Target>Windows System</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_15</RelationshipId>
        <RelationshipType>has</RelationshipType>
        <Source>Diplomatic and Government agencies</Source>
        <Target>Windows System</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_16</RelationshipId>
        <RelationshipType>trigger</RelationshipType>
        <Source>Diplomatic and Government agencies</Source>
        <Target>51EDEA56C1E83BCBC9F873168E2370AF</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_17</RelationshipId>
        <RelationshipType>related_to</RelationshipType>
        <Source>msmx21.exe</Source>
        <Target>svchost.exe</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_18</RelationshipId>
        <RelationshipType>related_to</RelationshipType>
        <Source>svchost.exe</Source>
        <Target>wsdktr.ltp</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_19</RelationshipId>
        <RelationshipType>related_to</RelationshipType>
        <Source>wsdktr.ltp</Source>
        <Target>nt-windows-online.com</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_20</RelationshipId>
        <RelationshipType>related_to</RelationshipType>
        <Source>wsdktr.ltp</Source>
        <Target>nt-windows-update.com</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_21</RelationshipId>
        <RelationshipType>related_to</RelationshipType>
        <Source>wsdktr.ltp</Source>
        <Target>nt-windows-check.com</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_22</RelationshipId>
        <RelationshipType>related_to</RelationshipType>
        <Source>wsdktr.ltp</Source>
        <Target>csrss-check-new.com</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_23</RelationshipId>
        <RelationshipType>related_to</RelationshipType>
        <Source>qagi.exe</Source>
        <Target>dezaa.ufy</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_24</RelationshipId>
        <RelationshipType>related_to</RelationshipType>
        <Source>dezaa.ufy</Source>
        <Target>29f2aad01fee3663.com</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_25</RelationshipId>
        <RelationshipType>related_to</RelationshipType>
        <Source>BackDoor-FJJ</Source>
        <Target>CVE-2011-3544</Target>
    </Relationship>
</Relationships>