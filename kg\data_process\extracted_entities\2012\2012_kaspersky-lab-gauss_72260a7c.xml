<Entitys>
    <!-- Attackers -->
    <Entity>
        <EntityId>entity_1</EntityId>
        <EntityName>Gauss</EntityName>
        <EntityVariantNames>
            <EntityVariantName>Gauss malware</EntityVariantName>
        </EntityVariantNames>
        <EntityType>attcker</EntityType>
        <EntitySubType>org</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
            <Label>TA0002</Label>
            <Label>TA0003</Label>
            <Label>TA0005</Label>
            <Label>TA0006</Label>
            <Label>TA0007</Label>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>1</Time>
        </Times>
        <Properties>
            <Property name="origin">Nation-state sponsored</Property>
            <Property name="first_seen">2011</Property>
            <Property name="target_regions">Middle East</Property>
        </Properties>
    </Entity>

    <!-- Victims -->
    <Entity>
        <EntityId>entity_2</EntityId>
        <EntityName>Lebanese Banks</EntityName>
        <EntityVariantNames>
            <EntityVariantName>Bank of Beirut</EntityVariantName>
            <EntityVariantName>Byblos Bank</EntityVariantName>
            <EntityVariantName>Fransabank</EntityVariantName>
        </EntityVariantNames>
        <EntityType>vctim</EntityType>
        <EntitySubType>org</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
        </Labels>
        <Times>
            <Time>2</Time>
        </Times>
        <Properties>
            <Property name="industry">Banking</Property>
            <Property name="country">Lebanon</Property>
        </Properties>
    </Entity>

    <!-- Tools -->
    <Entity>
        <EntityId>entity_3</EntityId>
        <EntityName>winshell.ocx</EntityName>
        <EntityType>tool</EntityType>
        <EntitySubType>malware</EntitySubType>
        <Labels>
            <Label>TA0006</Label>
            <Label>TA0007</Label>
        </Labels>
        <Times>
            <Time>3</Time>
        </Times>
        <Properties>
            <Property name="function">Banking credential theft</Property>
            <Property name="location">%system32%</Property>
        </Properties>
    </Entity>

    <Entity>
        <EntityId>entity_4</EntityId>
        <EntityName>dskapi.ocx</EntityName>
        <EntityType>tool</EntityType>
        <EntitySubType>malware</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
            <Label>TA0002</Label>
        </Labels>
        <Times>
            <Time>3</Time>
        </Times>
        <Properties>
            <Property name="function">USB infection</Property>
            <Property name="location">%system32%</Property>
        </Properties>
    </Entity>

    <!-- Vulnerabilities -->
    <Entity>
        <EntityId>entity_5</EntityId>
        <EntityName>CVE-2010-2568</EntityName>
        <EntityType>vul</EntityType>
        <EntitySubType>cve</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
        </Labels>
        <Times>
            <Time>2</Time>
        </Times>
        <Properties>
            <Property name="type">LNK vulnerability</Property>
        </Properties>
    </Entity>

    <!-- C2 Servers -->
    <Entity>
        <EntityId>entity_6</EntityId>
        <EntityName>*.gowin7.com</EntityName>
        <EntityType>asset</EntityType>
        <EntitySubType>domain</EntitySubType>
        <Labels>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>4</Time>
        </Times>
        <Properties>
            <Property name="ip">*************,**************</Property>
            <Property name="location">Portugal, India</Property>
        </Properties>
    </Entity>

    <Entity>
        <EntityId>entity_7</EntityId>
        <EntityName>*.secuurity.net</EntityName>
        <EntityType>asset</EntityType>
        <EntitySubType>domain</EntitySubType>
        <Labels>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>4</Time>
        </Times>
        <Properties>
            <Property name="ip">*************,**************</Property>
            <Property name="location">Portugal, India</Property>
        </Properties>
    </Entity>

    <!-- Attack Event -->
    <Entity>
        <EntityId>entity_8</EntityId>
        <EntityName>Gauss Cyber Attack</EntityName>
        <EntityType>event</EntityType>
        <EntitySubType>event</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
            <Label>TA0002</Label>
            <Label>TA0006</Label>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>1</Time>
        </Times>
        <Properties>
            <Property name="timeframe">2011-2012</Property>
            <Property name="primary_target">Middle East</Property>
        </Properties>
    </Entity>
</Entitys>
<Relationships>
    <Relationship>
        <RelationshipId>relationship_1</RelationshipId>
        <RelationshipType>involve</RelationshipType>
        <Source>Gauss Cyber Attack</Source>
        <Target>Gauss</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_2</RelationshipId>
        <RelationshipType>involve</RelationshipType>
        <Source>Gauss Cyber Attack</Source>
        <Target>Lebanese Banks</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_3</RelationshipId>
        <RelationshipType>use</RelationshipType>
        <Source>Gauss</Source>
        <Target>winshell.ocx</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_4</RelationshipId>
        <RelationshipType>use</RelationshipType>
        <Source>Gauss</Source>
        <Target>dskapi.ocx</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_5</RelationshipId>
        <RelationshipType>use</RelationshipType>
        <Source>Gauss</Source>
        <Target>CVE-2010-2568</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_6</RelationshipId>
        <RelationshipType>target</RelationshipType>
        <Source>Gauss</Source>
        <Target>Lebanese Banks</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_7</RelationshipId>
        <RelationshipType>related_to</RelationshipType>
        <Source>dskapi.ocx</Source>
        <Target>CVE-2010-2568</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_8</RelationshipType>
        <RelationshipType>related_to</RelationshipType>
        <Source>Gauss</Source>
        <Target>*.gowin7.com</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_9</RelationshipType>
        <RelationshipType>related_to</RelationshipType>
        <Source>Gauss</Source>
        <Target>*.secuurity.net</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_10</RelationshipType>
        <RelationshipType>exploit</RelationshipType>
        <Source>CVE-2010-2568</Source>
        <Target>Windows systems</Target>
    </Relationship>
</Relationships>