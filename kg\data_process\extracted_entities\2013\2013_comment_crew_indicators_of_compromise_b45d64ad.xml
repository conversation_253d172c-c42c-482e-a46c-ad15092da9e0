<Entitys>
    <Entity>
        <EntityId>entity_1</EntityId>
        <EntityName>Comment Crew</EntityName>
        <EntityVariantNames>
            <EntityVariantName>APT1</EntityVariantName>
        </EntityVariantNames>
        <EntityType>attcker</EntityType>
        <EntitySubType>org</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
            <Label>TA0002</Label>
            <Label>TA0003</Label>
            <Label>TA0004</Label>
            <Label>TA0005</Label>
            <Label>TA0006</Label>
            <Label>TA0007</Label>
            <Label>TA0008</Label>
            <Label>TA0009</Label>
            <Label>TA0011</Label>
            <Label>TA0010</Label>
        </Labels>
        <Times>
            <Time>1</Time>
        </Times>
    </Entity>
    <Entity>
        <EntityId>entity_2</EntityId>
        <EntityName>Comment Crew Indicators of Compromise</EntityName>
        <EntityType>event</EntityType>
        <EntitySubType>event</EntitySubType>
        <Labels>
            <Label>TA0001</Label>
            <Label>TA0002</Label>
            <Label>TA0003</Label>
            <Label>TA0004</Label>
            <Label>TA0005</Label>
            <Label>TA0006</Label>
            <Label>TA0007</Label>
            <Label>TA0008</Label>
            <Label>TA0009</Label>
            <Label>TA0011</Label>
            <Label>TA0010</Label>
        </Labels>
        <Times>
            <Time>1</Time>
        </Times>
        <Properties>
            <Property name="time">February 2013</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_3</EntityId>
        <EntityName>HTTP POST traffic</EntityName>
        <EntityType>ioc</EntityType>
        <EntitySubType>payload</EntitySubType>
        <Labels>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>2</Time>
        </Times>
        <Properties>
            <Property name="pattern">name=GeorgeBush&amp;userid=&lt;4 digit number&gt;&amp;other=</Property>
        </Properties>
    </Entity>
    <Entity>
        <EntityId>entity_4</EntityId>
        <EntityName>HTTP GET traffic</EntityName>
        <EntityType>ioc</EntityType>
        <EntitySubType>payload</EntitySubType>
        <Labels>
            <Label>TA0011</Label>
        </Labels>
        <Times>
            <Time>2</Time>
        </Times>
        <Properties>
            <Property name="paths">aspnet_client/report.asp, Resource/device_Tr.asp, images/device_index.asp, news/media/info.html, backsangho.jpg, addCats.asp, SmartNav.jpg, nblogo2.jpg</Property>
        </Properties>
    </Entity>
    <!-- Additional entities for domains, IPs, files, registry entries, etc. would follow the same pattern -->
</Entitys>
<Relationships>
    <Relationship>
        <RelationshipId>relationship_1</RelationshipId>
        <RelationshipType>involve</RelationshipType>
        <Source>Comment Crew Indicators of Compromise</Source>
        <Target>Comment Crew</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_2</RelationshipId>
        <RelationshipType>use</RelationshipType>
        <Source>Comment Crew</Source>
        <Target>HTTP POST traffic</Target>
    </Relationship>
    <Relationship>
        <RelationshipId>relationship_3</RelationshipId>
        <RelationshipType>use</RelationshipType>
        <Source>Comment Crew</Source>
        <Target>HTTP GET traffic</Target>
    </Relationship>
    <!-- Additional relationships would follow the same pattern -->
</Relationships>